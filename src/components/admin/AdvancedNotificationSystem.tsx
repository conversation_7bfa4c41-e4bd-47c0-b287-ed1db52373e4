import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Set<PERSON>s,
  Users,
  AlertTriangle,
  CheckCircle,
  Info,
  XCircle,
  Send,
  Filter,
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  Clock,
  Target,
  MessageSquare,
  Mail,
  Smartphone,
  Globe
} from 'lucide-react';
import { useAuth } from '../../context/AuthContext';

interface NotificationTemplate {
  _id: string;
  name: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  category: string;
  targetRoles: string[];
  targetDepartments: string[];
  channels: ('in-app' | 'email' | 'sms')[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface NotificationRule {
  _id: string;
  name: string;
  description: string;
  trigger: string;
  conditions: any[];
  template: string;
  isActive: boolean;
  priority: 'low' | 'medium' | 'high' | 'critical';
  createdAt: string;
}

interface NotificationHistory {
  _id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  recipients: number;
  sentAt: string;
  status: 'sent' | 'failed' | 'pending';
  channels: string[];
  template?: string;
}

export function AdvancedNotificationSystem() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [templates, setTemplates] = useState<NotificationTemplate[]>([]);
  const [rules, setRules] = useState<NotificationRule[]>([]);
  const [history, setHistory] = useState<NotificationHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');

  useEffect(() => {
    fetchNotificationData();
  }, []);

  const fetchNotificationData = async () => {
    try {
      setLoading(true);
      // Simulate API calls
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock templates
      const mockTemplates: NotificationTemplate[] = [
        {
          _id: '1',
          name: 'Patient Admission Alert',
          title: 'New Patient Admission',
          message: 'A new patient has been admitted to {department}. Patient ID: {patientId}',
          type: 'info',
          category: 'Patient Management',
          targetRoles: ['Doctor', 'Nurse', 'Receptionist'],
          targetDepartments: ['Emergency', 'General'],
          channels: ['in-app', 'email'],
          isActive: true,
          createdAt: '2024-01-15T10:00:00Z',
          updatedAt: '2024-01-15T10:00:00Z'
        },
        {
          _id: '2',
          name: 'Critical Lab Result',
          title: 'Critical Lab Result Alert',
          message: 'Critical lab result for patient {patientName}. Immediate attention required.',
          type: 'error',
          category: 'Laboratory',
          targetRoles: ['Doctor'],
          targetDepartments: ['All'],
          channels: ['in-app', 'email', 'sms'],
          isActive: true,
          createdAt: '2024-01-14T15:30:00Z',
          updatedAt: '2024-01-14T15:30:00Z'
        }
      ];

      // Mock rules
      const mockRules: NotificationRule[] = [
        {
          _id: '1',
          name: 'Emergency Patient Alert',
          description: 'Notify all emergency staff when a critical patient arrives',
          trigger: 'patient_admission',
          conditions: [{ field: 'priority', operator: 'equals', value: 'critical' }],
          template: '1',
          isActive: true,
          priority: 'critical',
          createdAt: '2024-01-15T10:00:00Z'
        }
      ];

      // Mock history
      const mockHistory: NotificationHistory[] = [
        {
          _id: '1',
          title: 'New Patient Admission',
          message: 'A new patient has been admitted to Emergency. Patient ID: PAT001',
          type: 'info',
          recipients: 15,
          sentAt: '2024-01-15T14:30:00Z',
          status: 'sent',
          channels: ['in-app', 'email'],
          template: 'Patient Admission Alert'
        },
        {
          _id: '2',
          title: 'Critical Lab Result Alert',
          message: 'Critical lab result for patient John Doe. Immediate attention required.',
          type: 'error',
          recipients: 5,
          sentAt: '2024-01-15T13:15:00Z',
          status: 'sent',
          channels: ['in-app', 'email', 'sms'],
          template: 'Critical Lab Result'
        }
      ];

      setTemplates(mockTemplates);
      setRules(mockRules);
      setHistory(mockHistory);
    } catch (error) {
      console.error('Error fetching notification data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle size={16} className="text-green-500" />;
      case 'warning':
        return <AlertTriangle size={16} className="text-yellow-500" />;
      case 'error':
        return <XCircle size={16} className="text-red-500" />;
      default:
        return <Info size={16} className="text-blue-500" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'error':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
    }
  };

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'email':
        return <Mail size={14} className="text-gray-500" />;
      case 'sms':
        return <Smartphone size={14} className="text-gray-500" />;
      case 'in-app':
        return <Bell size={14} className="text-gray-500" />;
      default:
        return <Globe size={14} className="text-gray-500" />;
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Bell },
    { id: 'templates', label: 'Templates', icon: MessageSquare },
    { id: 'rules', label: 'Rules', icon: Settings },
    { id: 'history', label: 'History', icon: Clock },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Advanced Notification System
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage role-based notifications and alerts
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
            <Plus size={16} />
            <span>Create Template</span>
          </button>
          <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
            <Send size={16} />
            <span>Send Notification</span>
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                }`}
              >
                <Icon size={16} />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                  <MessageSquare size={20} className="text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Templates</p>
                  <p className="text-xl font-semibold text-gray-900 dark:text-gray-100">{templates.length}</p>
                </div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                  <Settings size={20} className="text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Active Rules</p>
                  <p className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                    {rules.filter(r => r.isActive).length}
                  </p>
                </div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
                  <Send size={20} className="text-yellow-600 dark:text-yellow-400" />
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Sent Today</p>
                  <p className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                    {history.filter(h => new Date(h.sentAt).toDateString() === new Date().toDateString()).length}
                  </p>
                </div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                  <Users size={20} className="text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Total Recipients</p>
                  <p className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                    {history.reduce((sum, h) => sum + h.recipients, 0)}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Notifications */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Recent Notifications</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {history.slice(0, 5).map((notification) => (
                  <div key={notification._id} className="flex items-start space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex-shrink-0">
                      {getTypeIcon(notification.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {notification.title}
                        </h4>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {new Date(notification.sentAt).toLocaleString()}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        {notification.message}
                      </p>
                      <div className="flex items-center space-x-4 mt-2">
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {notification.recipients} recipients
                        </span>
                        <div className="flex items-center space-x-1">
                          {notification.channels.map((channel, index) => (
                            <span key={index} className="flex items-center">
                              {getChannelIcon(channel)}
                            </span>
                          ))}
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          notification.status === 'sent' 
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                            : notification.status === 'failed'
                            ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                        }`}>
                          {notification.status}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Templates Tab */}
      {activeTab === 'templates' && (
        <div className="space-y-6">
          {/* Search and Filter */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search templates..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  />
                </div>
              </div>
              <div>
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="all">All Types</option>
                  <option value="info">Info</option>
                  <option value="success">Success</option>
                  <option value="warning">Warning</option>
                  <option value="error">Error</option>
                </select>
              </div>
            </div>
          </div>

          {/* Templates Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {templates
              .filter(template => 
                (filterType === 'all' || template.type === filterType) &&
                (searchTerm === '' || template.name.toLowerCase().includes(searchTerm.toLowerCase()))
              )
              .map((template) => (
                <div key={template._id} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      {getTypeIcon(template.type)}
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                        {template.name}
                      </h3>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300">
                        <Edit size={16} />
                      </button>
                      <button className="text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                        <Eye size={16} />
                      </button>
                      <button className="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300">
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(template.type)}`}>
                        {template.type}
                      </span>
                    </div>
                    
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">
                        {template.title}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                        {template.message}
                      </p>
                    </div>
                    
                    <div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Target Roles:</p>
                      <div className="flex flex-wrap gap-1">
                        {template.targetRoles.map((role, index) => (
                          <span key={index} className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-xs rounded">
                            {role}
                          </span>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Channels:</p>
                      <div className="flex items-center space-x-2">
                        {template.channels.map((channel, index) => (
                          <span key={index} className="flex items-center space-x-1">
                            {getChannelIcon(channel)}
                            <span className="text-xs text-gray-600 dark:text-gray-400">{channel}</span>
                          </span>
                        ))}
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between pt-2 border-t border-gray-200 dark:border-gray-700">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        template.isActive 
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                          : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                      }`}>
                        {template.isActive ? 'Active' : 'Inactive'}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {template.category}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </div>
      )}

      {/* Other tabs would be implemented similarly */}
      {activeTab !== 'overview' && activeTab !== 'templates' && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8 text-center">
          <div className="text-gray-500 dark:text-gray-400">
            <Settings size={48} className="mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Coming Soon</h3>
            <p>This section is under development and will be available soon.</p>
          </div>
        </div>
      )}
    </div>
  );
}
