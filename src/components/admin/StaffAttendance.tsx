import React, { useState, useEffect } from 'react';
import {
  Clock,
  Calendar,
  Users,
  CheckCircle,
  XCircle,
  AlertCircle,
  Download,
  Filter,
  Search,
  Plus,
  Edit,
  Eye,
  MapPin,
  Timer
} from 'lucide-react';

interface AttendanceRecord {
  _id: string;
  employeeId: string;
  employeeName: string;
  department: string;
  position: string;
  date: string;
  checkIn: string | null;
  checkOut: string | null;
  status: 'present' | 'absent' | 'late' | 'half-day' | 'leave';
  workingHours: number;
  overtime: number;
  location: string;
  notes?: string;
}

interface Employee {
  _id: string;
  employeeId: string;
  name: string;
  department: string;
  position: string;
  email: string;
  phone: string;
  isActive: boolean;
}

export function StaffAttendance() {
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedDepartment, setSelectedDepartment] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showMarkAttendance, setShowMarkAttendance] = useState(false);

  useEffect(() => {
    fetchAttendanceData();
    fetchEmployees();
  }, [selectedDate, selectedDepartment]);

  const fetchAttendanceData = async () => {
    try {
      setLoading(true);
      // Simulate API call - replace with actual API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data
      const mockData: AttendanceRecord[] = [
        {
          _id: '1',
          employeeId: 'EMP001',
          employeeName: 'Dr. Sarah Johnson',
          department: 'Cardiology',
          position: 'Senior Doctor',
          date: selectedDate,
          checkIn: '08:30',
          checkOut: '17:30',
          status: 'present',
          workingHours: 9,
          overtime: 0,
          location: 'Main Building'
        },
        {
          _id: '2',
          employeeId: 'EMP002',
          employeeName: 'Nurse Mary Wilson',
          department: 'Emergency',
          position: 'Head Nurse',
          date: selectedDate,
          checkIn: '09:15',
          checkOut: '18:00',
          status: 'late',
          workingHours: 8.75,
          overtime: 0.75,
          location: 'Emergency Wing'
        },
        {
          _id: '3',
          employeeId: 'EMP003',
          employeeName: 'Dr. Michael Brown',
          department: 'Neurology',
          position: 'Consultant',
          date: selectedDate,
          checkIn: null,
          checkOut: null,
          status: 'absent',
          workingHours: 0,
          overtime: 0,
          location: 'N/A'
        }
      ];

      setAttendanceRecords(mockData);
    } catch (error) {
      console.error('Error fetching attendance data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchEmployees = async () => {
    try {
      // Mock data
      const mockEmployees: Employee[] = [
        {
          _id: '1',
          employeeId: 'EMP001',
          name: 'Dr. Sarah Johnson',
          department: 'Cardiology',
          position: 'Senior Doctor',
          email: '<EMAIL>',
          phone: '+1234567890',
          isActive: true
        },
        {
          _id: '2',
          employeeId: 'EMP002',
          name: 'Nurse Mary Wilson',
          department: 'Emergency',
          position: 'Head Nurse',
          email: '<EMAIL>',
          phone: '+1234567891',
          isActive: true
        }
      ];

      setEmployees(mockEmployees);
    } catch (error) {
      console.error('Error fetching employees:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'present':
        return <CheckCircle size={16} className="text-green-500" />;
      case 'late':
        return <AlertCircle size={16} className="text-yellow-500" />;
      case 'absent':
        return <XCircle size={16} className="text-red-500" />;
      case 'half-day':
        return <Clock size={16} className="text-blue-500" />;
      case 'leave':
        return <Calendar size={16} className="text-purple-500" />;
      default:
        return <AlertCircle size={16} className="text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'present':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'late':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'absent':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'half-day':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'leave':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const filteredRecords = attendanceRecords.filter(record => {
    const matchesDepartment = selectedDepartment === 'all' || record.department === selectedDepartment;
    const matchesSearch = record.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.employeeId.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesDepartment && matchesSearch;
  });

  const departments = [...new Set(attendanceRecords.map(record => record.department))];

  const attendanceStats = {
    total: filteredRecords.length,
    present: filteredRecords.filter(r => r.status === 'present').length,
    late: filteredRecords.filter(r => r.status === 'late').length,
    absent: filteredRecords.filter(r => r.status === 'absent').length,
    leave: filteredRecords.filter(r => r.status === 'leave').length
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Staff Attendance
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Track and manage staff attendance records
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setShowMarkAttendance(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <Plus size={16} />
            <span>Mark Attendance</span>
          </button>
          <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
            <Download size={16} />
            <span>Export</span>
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Date
            </label>
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Department
            </label>
            <select
              value={selectedDepartment}
              onChange={(e) => setSelectedDepartment(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              <option value="all">All Departments</option>
              {departments.map(dept => (
                <option key={dept} value={dept}>{dept}</option>
              ))}
            </select>
          </div>
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Search Employee
            </label>
            <div className="relative">
              <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search by name or employee ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <Users size={20} className="text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Staff</p>
              <p className="text-xl font-semibold text-gray-900 dark:text-gray-100">{attendanceStats.total}</p>
            </div>
          </div>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <CheckCircle size={20} className="text-green-600 dark:text-green-400" />
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Present</p>
              <p className="text-xl font-semibold text-gray-900 dark:text-gray-100">{attendanceStats.present}</p>
            </div>
          </div>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
              <AlertCircle size={20} className="text-yellow-600 dark:text-yellow-400" />
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Late</p>
              <p className="text-xl font-semibold text-gray-900 dark:text-gray-100">{attendanceStats.late}</p>
            </div>
          </div>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-red-100 dark:bg-red-900/20 rounded-lg">
              <XCircle size={20} className="text-red-600 dark:text-red-400" />
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Absent</p>
              <p className="text-xl font-semibold text-gray-900 dark:text-gray-100">{attendanceStats.absent}</p>
            </div>
          </div>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
              <Calendar size={20} className="text-purple-600 dark:text-purple-400" />
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">On Leave</p>
              <p className="text-xl font-semibold text-gray-900 dark:text-gray-100">{attendanceStats.leave}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Attendance Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Attendance Records - {new Date(selectedDate).toLocaleDateString()}
          </h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Employee</th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Department</th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Check In</th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Check Out</th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Working Hours</th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Status</th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Location</th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-500 dark:text-gray-300">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {filteredRecords.map((record) => (
                <tr key={record._id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="py-4 px-6">
                    <div>
                      <div className="font-medium text-gray-900 dark:text-gray-100">{record.employeeName}</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">{record.employeeId}</div>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div>
                      <div className="text-sm text-gray-900 dark:text-gray-100">{record.department}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{record.position}</div>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center space-x-2">
                      <Clock size={14} className="text-gray-400" />
                      <span className="text-sm text-gray-900 dark:text-gray-100">
                        {record.checkIn || 'Not checked in'}
                      </span>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center space-x-2">
                      <Clock size={14} className="text-gray-400" />
                      <span className="text-sm text-gray-900 dark:text-gray-100">
                        {record.checkOut || 'Not checked out'}
                      </span>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center space-x-2">
                      <Timer size={14} className="text-gray-400" />
                      <span className="text-sm text-gray-900 dark:text-gray-100">
                        {record.workingHours}h
                        {record.overtime > 0 && (
                          <span className="text-blue-600 dark:text-blue-400 ml-1">
                            (+{record.overtime}h OT)
                          </span>
                        )}
                      </span>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(record.status)}
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(record.status)}`}>
                        {record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                      </span>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center space-x-2">
                      <MapPin size={14} className="text-gray-400" />
                      <span className="text-sm text-gray-900 dark:text-gray-100">{record.location}</span>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center space-x-2">
                      <button className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300">
                        <Edit size={16} />
                      </button>
                      <button className="text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                        <Eye size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
