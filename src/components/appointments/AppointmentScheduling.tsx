import React, { useState, useEffect } from 'react';
import { 
  Calendar, 
  Clock, 
  User, 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  Check<PERSON>ircle, 
  XCircle, 
  AlertCircle,
  Phone,
  Mail,
  MapPin,
  Stethoscope,
  Filter,
  Download,
  Loader,
  X
} from 'lucide-react';
import { appointmentAPI, patientAPI, adminAPI } from '../../services/apiService';

interface Doctor {
  _id: string;
  firstName: string;
  lastName: string;
  department: string;
  specialization: string;
  email: string;
  phone: string;
  availability: Array<{
    dayOfWeek: number;
    startTime: string;
    endTime: string;
    isAvailable: boolean;
  }>;
}

interface Patient {
  _id: string;
  patientId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth: string;
}

interface Appointment {
  _id: string;
  appointmentId: string;
  patient: Patient;
  doctor: Doctor;
  appointmentDate: string;
  appointmentTime: string;
  duration: number;
  appointmentType: string;
  department: string;
  reason: string;
  status: string;
  priority: string;
  notes?: string;
  reminderSent: boolean;
  createdAt: string;
  updatedAt: string;
}

interface TimeSlot {
  time: string;
  available: boolean;
  appointmentId?: string;
}

export function AppointmentScheduling() {
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedDoctor, setSelectedDoctor] = useState<string>('');
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showNewAppointmentModal, setShowNewAppointmentModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [doctorLoading, setDoctorLoading] = useState(false);
  const [doctorError, setDoctorError] = useState<string | null>(null);
  const [patientLoading, setPatientLoading] = useState(false);
  const [patientError, setPatientError] = useState<string | null>(null);
  const [roles, setRoles] = useState<any[]>([]);
  const [rolesLoading, setRolesLoading] = useState(false);
  const [rolesError, setRolesError] = useState<string | null>(null);
  const [doctorRoleId, setDoctorRoleId] = useState<string | null>(null);

  // New appointment form state
  const [newAppointment, setNewAppointment] = useState({
    patientId: '',
    doctorId: '',
    department: '',
    date: selectedDate,
    time: '',
    type: '',
    priority: 'Medium',
    reason: '',
    notes: '',
  });
  const [formError, setFormError] = useState<string | null>(null);

  const appointmentTypes = [
    'Consultation', 'Follow-up', 'Routine Check-up', 'Emergency', 
    'Procedure', 'Vaccination', 'Screening', 'Surgery'
  ];

  const departments = [
    'Cardiology', 'Neurology', 'Orthopedics', 'Pediatrics', 
    'General Medicine', 'Emergency Medicine', 'Surgery', 'Psychiatry'
  ];

  const appointmentStatuses = [
    'Scheduled', 'Confirmed', 'In Progress', 'Completed', 
    'Cancelled', 'No Show', 'Rescheduled'
  ];

  // Fetch roles on mount
  useEffect(() => {
    const fetchRoles = async () => {
      setRolesLoading(true);
      setRolesError(null);
      try {
        const res = await adminAPI.getRoles();
        const rolesArr = res.data || res.roles || res || [];
        setRoles(Array.isArray(rolesArr) ? rolesArr : []);
        // Find Doctor role (case-insensitive)
        const doctorRole = rolesArr.find((r: any) => r.name && r.name.toLowerCase() === 'doctor');
        if (doctorRole && doctorRole._id) {
          setDoctorRoleId(doctorRole._id);
        } else {
          setDoctorRoleId(null);
        }
      } catch (err: any) {
        setRolesError(err.message || 'Failed to fetch roles');
        setRoles([]);
        setDoctorRoleId(null);
      } finally {
        setRolesLoading(false);
      }
    };
    fetchRoles();
  }, []);

  useEffect(() => {
    fetchAppointments();
    fetchDoctors();
    fetchPatients();
  }, [selectedDate, selectedDoctor, selectedDepartment]);

  useEffect(() => {
    if (selectedDoctor && selectedDate) {
      generateTimeSlots();
    }
  }, [selectedDoctor, selectedDate, appointments]);

  // Robust doctor fetch with fallback
  const fetchDoctors = async () => {
    setDoctorLoading(true);
    setDoctorError(null);
    try {
      let users = [];
      if (doctorRoleId) {
        const data = await adminAPI.getUsers({ role: doctorRoleId });
        users = data.users || data.data || data || [];
      } else {
        // Fallback: try fetching all users and filter by role name 'doctor'
        const data = await adminAPI.getUsers();
        users = (data.users || data.data || data || []).filter((u: any) => u.role && u.role.name && u.role.name.toLowerCase() === 'doctor');
      }
      setDoctors(Array.isArray(users) ? users : []);
    } catch (err: any) {
      setDoctorError(err.message || 'Failed to fetch doctors');
      setDoctors([]);
    } finally {
      setDoctorLoading(false);
    }
  };

  // Robust patient fetch
  const fetchPatients = async () => {
    setPatientLoading(true);
    setPatientError(null);
    try {
      const data = await patientAPI.getAll({ limit: 100 });
      const patientsArr = data.data || data.patients || data.users || data || [];
      setPatients(Array.isArray(patientsArr) ? patientsArr : []);
    } catch (err: any) {
      setPatientError(err.message || 'Failed to fetch patients');
      setPatients([]);
    } finally {
      setPatientLoading(false);
    }
  };

  // Defensive appointments fetch
  const fetchAppointments = async () => {
    try {
      setLoading(true);
      setError(null);
      const params: any = {};
      if (selectedDate) params.date = selectedDate;
      if (selectedDoctor) params.doctorId = selectedDoctor;
      if (selectedDepartment) params.department = selectedDepartment;
      if (filterStatus !== 'all') params.status = filterStatus;
      const data = await appointmentAPI.getAll(params);
      let arr = data.appointments || data.data || data || [];
      if (!Array.isArray(arr)) {
        // Try to extract array from object
        arr = Object.values(arr).find(v => Array.isArray(v)) || [];
      }
      setAppointments(Array.isArray(arr) ? arr : []);
      // Debug log
      if (!Array.isArray(arr)) {
        // eslint-disable-next-line no-console
        console.log('Appointments API response (not array):', data);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch appointments');
      setAppointments([]);
    } finally {
      setLoading(false);
    }
  };

  const generateTimeSlots = () => {
    const doctor = doctors.find(d => d._id === selectedDoctor);
    if (!doctor) return;

    const selectedDateObj = new Date(selectedDate);
    const dayOfWeek = selectedDateObj.getDay();
    
    const availability = doctor.availability.find(a => a.dayOfWeek === dayOfWeek);
    if (!availability || !availability.isAvailable) {
      setTimeSlots([]);
      return;
    }

    const slots: TimeSlot[] = [];
    const startTime = new Date(`${selectedDate}T${availability.startTime}`);
    const endTime = new Date(`${selectedDate}T${availability.endTime}`);
    
    const current = new Date(startTime);
    while (current < endTime) {
      const timeString = current.toTimeString().slice(0, 5);
      
      // Check if this time slot is already booked
      const existingAppointment = appointments.find(apt => 
        apt.doctor._id === selectedDoctor && 
        apt.appointmentDate === selectedDate && 
        apt.appointmentTime === timeString &&
        apt.status !== 'Cancelled'
      );

      slots.push({
        time: timeString,
        available: !existingAppointment,
        appointmentId: existingAppointment?._id
      });

      current.setMinutes(current.getMinutes() + 30); // 30-minute slots
    }

    setTimeSlots(slots);
  };

  // Helper: get available time slots for selected doctor/date
  const availableTimeSlots = () => {
    if (!newAppointment.doctorId || !newAppointment.date) return [];
    const doctor = doctors.find(d => d._id === newAppointment.doctorId);
    if (!doctor || !Array.isArray(doctor.availability)) return [];
    const selectedDateObj = new Date(newAppointment.date);
    const dayOfWeek = selectedDateObj.getDay();
    const availability = doctor.availability.find(a => a.dayOfWeek === dayOfWeek);
    if (!availability || !availability.isAvailable) return [];
    const slots: string[] = [];
    const startTime = new Date(`${newAppointment.date}T${availability.startTime}`);
    const endTime = new Date(`${newAppointment.date}T${availability.endTime}`);
    const current = new Date(startTime);
    while (current < endTime) {
      const timeString = current.toTimeString().slice(0, 5);
      // Check if slot is already booked
      const existing = appointments.find(
        apt => apt.doctor._id === newAppointment.doctorId &&
          apt.appointmentDate === newAppointment.date &&
          apt.appointmentTime === timeString &&
          apt.status !== 'Cancelled'
      );
      if (!existing) slots.push(timeString);
      current.setMinutes(current.getMinutes() + 30);
    }
    return slots;
  };

  // Handle form field change
  const handleFormChange = (field: string, value: string) => {
    setNewAppointment(prev => ({ ...prev, [field]: value }));
    if (field === 'doctorId') {
      // Auto-set department if doctor selected
      const doc = doctors.find(d => d._id === value);
      if (doc) setNewAppointment(prev => ({ ...prev, department: doc.department }));
    }
    if (field === 'date' && value) {
      setNewAppointment(prev => ({ ...prev, time: '' })); // reset time if date changes
    }
  };

  // Handle form submit
  const handleAddAppointment = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);
    const { patientId, doctorId, department, date, time, type, priority, reason } = newAppointment;
    if (!patientId || !doctorId || !department || !date || !time || !type || !priority || !reason) {
      setFormError('Please fill in all required fields.');
      return;
    }
    try {
      setLoading(true);
      // Compose payload for API
      const payload = {
        patient: patientId,
        doctor: doctorId,
        department,
        appointmentDate: date,
        appointmentTime: time,
        type,
        priority,
        reason,
        notes: newAppointment.notes,
      };
      await appointmentAPI.create(payload);
      setShowNewAppointmentModal(false);
      setNewAppointment({
        patientId: '',
        doctorId: '',
        department: '',
        date: selectedDate,
        time: '',
        type: '',
        priority: 'Medium',
        reason: '',
        notes: '',
      });
      await fetchAppointments(); // Refresh list
    } catch (err: any) {
      setFormError(err.message || 'Failed to add appointment');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Scheduled':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'Confirmed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'In Progress':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'Completed':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
      case 'Cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'No Show':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'Rescheduled':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Scheduled':
        return <Clock size={14} className="text-blue-600" />;
      case 'Confirmed':
        return <CheckCircle size={14} className="text-green-600" />;
      case 'In Progress':
        return <Stethoscope size={14} className="text-yellow-600" />;
      case 'Completed':
        return <CheckCircle size={14} className="text-gray-600" />;
      case 'Cancelled':
        return <XCircle size={14} className="text-red-600" />;
      case 'No Show':
        return <AlertCircle size={14} className="text-orange-600" />;
      default:
        return <Clock size={14} className="text-gray-600" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Urgent':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'High':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'Medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'Low':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
    }
  };

  const updateAppointmentStatus = async (appointmentId: string, newStatus: string) => {
    try {
      // Update appointment status - replace with actual API call
      setAppointments(prev => prev.map(apt => 
        apt._id === appointmentId 
          ? { ...apt, status: newStatus, updatedAt: new Date().toISOString() }
          : apt
      ));
    } catch (err) {
      console.error('Error updating appointment status:', err);
    }
  };

  // Defensive filter for appointments
  const filteredAppointments = Array.isArray(appointments)
    ? appointments.filter(appointment => {
    const matchesSearch = searchTerm === '' || 
      appointment.patient.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.patient.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.patient.patientId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.doctor.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.doctor.lastName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || appointment.status === filterStatus;
    return matchesSearch && matchesStatus;
      })
    : [];

  // Error boundary for main render
  if (error || doctorError || patientError) {
    return (
      <div className="p-8 text-center text-red-600 dark:text-red-400">
        <h2 className="text-xl font-bold mb-2">An error occurred</h2>
        {error && <div>Appointments: {error}</div>}
        {doctorError && <div>Doctors: {doctorError}</div>}
        {patientError && <div>Patients: {patientError}</div>}
        <button className="mt-4 px-4 py-2 rounded bg-blue-600 text-white" onClick={() => window.location.reload()}>Reload</button>
      </div>
    );
  }

  // Add error boundary for roles
  if (rolesError) {
    return (
      <div className="p-8 text-center text-red-600 dark:text-red-400">
        <h2 className="text-xl font-bold mb-2">An error occurred</h2>
        <div>Roles: {rolesError}</div>
        <button className="mt-4 px-4 py-2 rounded bg-blue-600 text-white" onClick={() => window.location.reload()}>Reload</button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Appointment Scheduling</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">Manage patient appointments and doctor schedules</p>
        </div>
        <button
          onClick={() => setShowNewAppointmentModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-800 transition-colors flex items-center space-x-2"
        >
          <Plus size={20} />
          <span>New Appointment</span>
        </button>
      </div>

      {/* Filters and Search */}
      <div className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Date</label>
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-gray-100"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Department</label>
            <select
              value={selectedDepartment}
              onChange={(e) => setSelectedDepartment(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-gray-100"
            >
              <option value="">All Departments</option>
              {departments.map(dept => (
                <option key={dept} value={dept}>{dept}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Doctor</label>
            <select
              value={selectedDoctor}
              onChange={(e) => setSelectedDoctor(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-gray-100"
            >
              <option value="">All Doctors</option>
              {doctors
                .filter(doctor => !selectedDepartment || doctor.department === selectedDepartment)
                .map(doctor => (
                  <option key={doctor._id} value={doctor._id}>
                    {doctor.firstName} {doctor.lastName} - {doctor.department}
                  </option>
                ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-gray-100"
            >
              <option value="all">All Status</option>
              {appointmentStatuses.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="mt-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" size={20} />
            <input
              type="text"
              placeholder="Search by patient name, ID, or doctor..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-gray-100"
            />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Time Slots */}
        {selectedDoctor && (
          <div className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Available Time Slots - {selectedDate}
            </h3>
            
            {timeSlots.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 text-center py-8">
                No availability for selected date
              </p>
            ) : (
              <div className="grid grid-cols-2 gap-2">
                {timeSlots.map((slot, index) => (
                  <button
                    key={index}
                    disabled={!slot.available}
                    className={`p-3 rounded-lg text-sm font-medium transition-colors ${
                      slot.available
                        ? 'bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900 dark:text-green-200 dark:hover:bg-green-800'
                        : 'bg-red-100 text-red-800 cursor-not-allowed dark:bg-red-900 dark:text-red-200'
                    }`}
                  >
                    {slot.time}
                  </button>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Appointments List */}
        <div className={`bg-white dark:bg-gray-900 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 ${selectedDoctor ? 'lg:col-span-2' : 'lg:col-span-3'}`}>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Appointments for {new Date(selectedDate).toLocaleDateString()}
            </h3>
            <div className="flex items-center space-x-2">
              <button className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 p-2">
                <Filter size={16} />
              </button>
              <button className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 p-2">
                <Download size={16} />
              </button>
            </div>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader className="animate-spin h-8 w-8 text-blue-500 dark:text-blue-400" />
            </div>
          ) : filteredAppointments.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No appointments found</h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                No appointments scheduled for the selected criteria.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredAppointments.map((appointment) => (
                <div
                  key={appointment._id}
                  className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                        <User size={20} className="text-blue-600 dark:text-blue-300" />
                      </div>
                      
                      <div>
                        <h4 className="font-semibold text-gray-900 dark:text-gray-100">
                          {appointment.patient.firstName} {appointment.patient.lastName}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          ID: {appointment.patient.patientId}
                        </p>
                        <div className="flex items-center space-x-4 mt-1">
                          <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                            <Clock size={12} className="mr-1" />
                            {appointment.appointmentTime}
                          </span>
                          <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                            <Stethoscope size={12} className="mr-1" />
                            {appointment.doctor.firstName} {appointment.doctor.lastName}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <span className={`flex items-center px-2 py-1 text-xs font-semibold rounded-full max-w-[110px] truncate whitespace-nowrap ${getPriorityColor(appointment.priority)}`}> 
                        {appointment.priority}
                      </span>
                      <span className={`flex items-center px-2 py-1 text-xs font-semibold rounded-full max-w-[130px] truncate whitespace-nowrap ${getStatusColor(appointment.status)}`}>
                        {getStatusIcon(appointment.status)}
                        <span className="ml-1">{appointment.status}</span>
                      </span>
                    </div>
                  </div>

                  <div className="mt-3 text-sm text-gray-600 dark:text-gray-300">
                    <p><strong>Type:</strong> {appointment.appointmentType}</p>
                    <p><strong>Reason:</strong> {appointment.reason}</p>
                    {appointment.notes && (
                      <p><strong>Notes:</strong> {appointment.notes}</p>
                    )}
                  </div>

                  <div className="mt-3 flex items-center justify-between">
                    <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                      <span className="flex items-center">
                        <Phone size={12} className="mr-1" />
                        {appointment.patient.phone}
                      </span>
                      <span className="flex items-center">
                        <Mail size={12} className="mr-1" />
                        {appointment.patient.email}
                      </span>
                    </div>

                    <div className="flex items-center space-x-2">
                      {appointment.status === 'Scheduled' && (
                        <button
                          onClick={() => updateAppointmentStatus(appointment._id, 'Confirmed')}
                          className="bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700 dark:hover:bg-green-800"
                        >
                          Confirm
                        </button>
                      )}
                      {appointment.status === 'Confirmed' && (
                        <button
                          onClick={() => updateAppointmentStatus(appointment._id, 'In Progress')}
                          className="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700 dark:hover:bg-blue-800"
                        >
                          Start
                        </button>
                      )}
                      <button className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 p-1">
                        <Edit size={14} />
                      </button>
                      <button className="text-red-400 hover:text-red-600 dark:text-red-500 dark:hover:text-red-300 p-1">
                        <Trash2 size={14} />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400 dark:text-red-200" />
            <div className="ml-3">
              <p className="text-sm text-red-700 dark:text-red-200">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* New Appointment Modal */}
      {showNewAppointmentModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 dark:bg-black/70">
          <div className="bg-white dark:bg-gray-900 rounded-xl shadow-xl w-full max-w-lg p-6 relative border border-gray-200 dark:border-gray-700">
            <button
              className="absolute top-3 right-3 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
              onClick={() => setShowNewAppointmentModal(false)}
              aria-label="Close"
            >
              <X size={20} />
            </button>
            <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">Add New Appointment</h2>
            {formError && <div className="mb-2 text-red-600 dark:text-red-400 text-sm">{formError}</div>}
            <form onSubmit={handleAddAppointment} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Patient<span className="text-red-500">*</span></label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg dark:bg-gray-800 dark:text-gray-100"
                  value={newAppointment.patientId}
                  onChange={e => handleFormChange('patientId', e.target.value)}
                  required
                >
                  <option value="">Select patient</option>
                  {patients.map(p => (
                    <option key={p._id} value={p._id}>{p.firstName} {p.lastName} ({p.patientId})</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Doctor<span className="text-red-500">*</span></label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg dark:bg-gray-800 dark:text-gray-100"
                  value={newAppointment.doctorId}
                  onChange={e => handleFormChange('doctorId', e.target.value)}
                  required
                >
                  <option value="">Select doctor</option>
                  {doctors.map(d => (
                    <option key={d._id} value={d._id}>{d.firstName} {d.lastName} ({d.department})</option>
                  ))}
                </select>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Department<span className="text-red-500">*</span></label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg dark:bg-gray-800 dark:text-gray-100"
                    value={newAppointment.department}
                    onChange={e => handleFormChange('department', e.target.value)}
                    required
                  >
                    <option value="">Select department</option>
                    {departments.map(dept => (
                      <option key={dept} value={dept}>{dept}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Date<span className="text-red-500">*</span></label>
                  <input
                    type="date"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg dark:bg-gray-800 dark:text-gray-100"
                    value={newAppointment.date}
                    onChange={e => handleFormChange('date', e.target.value)}
                    required
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Time Slot<span className="text-red-500">*</span></label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg dark:bg-gray-800 dark:text-gray-100"
                    value={newAppointment.time}
                    onChange={e => handleFormChange('time', e.target.value)}
                    required
                  >
                    <option value="">Select time</option>
                    {availableTimeSlots().map(slot => (
                      <option key={slot} value={slot}>{slot}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Type<span className="text-red-500">*</span></label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg dark:bg-gray-800 dark:text-gray-100"
                    value={newAppointment.type}
                    onChange={e => handleFormChange('type', e.target.value)}
                    required
                  >
                    <option value="">Select type</option>
                    {appointmentTypes.map(type => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Priority<span className="text-red-500">*</span></label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg dark:bg-gray-800 dark:text-gray-100"
                    value={newAppointment.priority}
                    onChange={e => handleFormChange('priority', e.target.value)}
                    required
                  >
                    <option value="Urgent">Urgent</option>
                    <option value="High">High</option>
                    <option value="Medium">Medium</option>
                    <option value="Low">Low</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Reason<span className="text-red-500">*</span></label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg dark:bg-gray-800 dark:text-gray-100"
                    value={newAppointment.reason}
                    onChange={e => handleFormChange('reason', e.target.value)}
                    required
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Notes</label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg dark:bg-gray-800 dark:text-gray-100"
                  value={newAppointment.notes}
                  onChange={e => handleFormChange('notes', e.target.value)}
                  rows={2}
                />
              </div>
              <div className="flex justify-end space-x-2 pt-2">
                <button
                  type="button"
                  className="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700"
                  onClick={() => setShowNewAppointmentModal(false)}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700 dark:hover:bg-blue-800 font-semibold"
                >
                  Add Appointment
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
