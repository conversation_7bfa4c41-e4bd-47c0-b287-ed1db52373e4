import React, { useState, useEffect } from 'react';
import {
  Shield,
  Users,
  Key,
  Settings,
  Activity,
  BarChart3,
  <PERSON><PERSON><PERSON><PERSON>gle,
  <PERSON>r<PERSON><PERSON><PERSON>,
  Bell,
  Clock,
  ChevronRight,
  ArrowLeft
} from 'lucide-react';
import { useAuth } from '../context/AuthContext';

// Import clean, dedicated components
import { UserManagement } from './admin/UserManagement';
import { RoleManagement } from './admin/RoleManagement';
import { PermissionManagement } from './admin/PermissionManagement';
import { AuditSystem } from './admin/AuditSystem';
import { SystemSettings } from './admin/SystemSettings';
import { SystemDashboard } from './admin/SystemDashboard';
import { StaffAttendance } from './admin/StaffAttendance';
import { AdvancedNotificationSystem } from './admin/AdvancedNotificationSystem';

interface AdminSection {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  description: string;
  requiredLevel?: number;
  subMenus?: AdminSubMenu[];
}

interface AdminSubMenu {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  component: React.ComponentType<any>;
  requiredLevel?: number;
  description: string;
}

const adminSections: AdminSection[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: BarChart3,
    description: 'System overview and statistics',
    subMenus: [
      {
        id: 'overview',
        label: 'System Overview',
        icon: BarChart3,
        component: SystemDashboard,
        description: 'View system statistics and health metrics'
      }
    ]
  },
  {
    id: 'user-management',
    label: 'User Management',
    icon: Users,
    description: 'Manage user accounts and profiles',
    subMenus: [
      {
        id: 'users',
        label: 'User Accounts',
        icon: Users,
        component: UserManagement,
        description: 'Manage user accounts and profiles'
      },
      {
        id: 'roles',
        label: 'Role Management',
        icon: Shield,
        component: RoleManagement,
        requiredLevel: 8,
        description: 'Manage roles and role hierarchy'
      },
      {
        id: 'permissions',
        label: 'Permissions',
        icon: Key,
        component: PermissionManagement,
        requiredLevel: 9,
        description: 'Manage system permissions'
      }
    ]
  },
  {
    id: 'staff-management',
    label: 'Staff Management',
    icon: UserCheck,
    description: 'Manage staff and attendance',
    subMenus: [
      {
        id: 'attendance',
        label: 'Attendance Tracking',
        icon: Clock,
        component: StaffAttendance,
        description: 'Track staff attendance and working hours'
      }
    ]
  },
  {
    id: 'notifications',
    label: 'Notification System',
    icon: Bell,
    description: 'Manage system notifications',
    subMenus: [
      {
        id: 'notification-center',
        label: 'Notification Center',
        icon: Bell,
        component: AdvancedNotificationSystem,
        description: 'Manage and configure system notifications'
      }
    ]
  },
  {
    id: 'audit',
    label: 'Audit Logs',
    icon: Activity,
    description: 'View system audit logs and activity',
    subMenus: [
      {
        id: 'audit-logs',
        label: 'System Audit',
        icon: Activity,
        component: AuditSystem,
        description: 'View system audit logs and activity'
      }
    ]
  },
  {
    id: 'settings',
    label: 'System Settings',
    icon: Settings,
    description: 'Configure system settings',
    requiredLevel: 10,
    subMenus: [
      {
        id: 'system-config',
        label: 'System Configuration',
        icon: Settings,
        component: SystemSettings,
        requiredLevel: 10,
        description: 'Configure system settings and preferences'
      }
    ]
  }
];

export function Administration() {
  const { user } = useAuth();
  const [activeSection, setActiveSection] = useState<string | null>(null);
  const [activeSubMenu, setActiveSubMenu] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Initialize admin section
    const initializeAdmin = async () => {
      try {
        setLoading(true);
        // Check if user has admin access
        if (!user?.role || user.role.level < 5) {
          setError('Access denied. Insufficient permissions.');
          return;
        }

        // Simulate initialization
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (err) {
        setError('Failed to initialize administration panel');
      } finally {
        setLoading(false);
      }
    };

    initializeAdmin();
  }, [user]);

  // Filter sections based on user permissions
  const availableSections = adminSections.filter(section => {
    if (!section.requiredLevel) return true;
    return user?.role?.level >= section.requiredLevel;
  });

  // Filter sub-menus based on user permissions
  const getAvailableSubMenus = (section: AdminSection) => {
    if (!section.subMenus) return [];
    return section.subMenus.filter(subMenu => {
      if (!subMenu.requiredLevel) return true;
      return user?.role?.level >= subMenu.requiredLevel;
    });
  };

  // Ensure user has at least one available section
  if (availableSections.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle size={48} className="text-yellow-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">No Admin Access</h2>
          <p className="text-gray-600 dark:text-gray-400">
            You don't have permission to access any admin functions.
          </p>
        </div>
      </div>
    );
  }

  // Get current component to render
  const getCurrentComponent = () => {
    if (!activeSection || !activeSubMenu) return null;

    const section = availableSections.find(s => s.id === activeSection);
    if (!section) return null;

    const subMenus = getAvailableSubMenus(section);
    const subMenu = subMenus.find(sm => sm.id === activeSubMenu);

    return subMenu?.component || null;
  };

  const CurrentComponent = getCurrentComponent();

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading administration panel...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle size={48} className="text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">Access Error</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Show main menu if no section is selected
  if (!activeSection) {
    return (
      <div className="h-full bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-6xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              Administration
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Manage system settings, users, and configurations
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {availableSections.map((section) => {
              const Icon = section.icon;
              const availableSubMenus = getAvailableSubMenus(section);

              return (
                <div
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-all duration-200 cursor-pointer group"
                >
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg group-hover:bg-blue-100 dark:group-hover:bg-blue-900/30 transition-colors">
                      <Icon size={24} className="text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                        {section.label}
                      </h3>
                    </div>
                    <ChevronRight size={20} className="text-gray-400 group-hover:text-blue-500 transition-colors" />
                  </div>

                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    {section.description}
                  </p>

                  <div className="text-sm text-gray-500 dark:text-gray-500">
                    {availableSubMenus.length} {availableSubMenus.length === 1 ? 'option' : 'options'} available
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  }
  // Show sub-menu if section is selected but no sub-menu is active
  const currentSection = availableSections.find(s => s.id === activeSection);
  const availableSubMenus = currentSection ? getAvailableSubMenus(currentSection) : [];

  if (!activeSubMenu) {
    return (
      <div className="h-full bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-6xl mx-auto">
          <div className="mb-8">
            <button
              onClick={() => setActiveSection(null)}
              className="flex items-center space-x-2 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 mb-4"
            >
              <ArrowLeft size={20} />
              <span>Back to Administration</span>
            </button>

            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              {currentSection?.label}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {currentSection?.description}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {availableSubMenus.map((subMenu) => {
              const Icon = subMenu.icon;

              return (
                <div
                  key={subMenu.id}
                  onClick={() => setActiveSubMenu(subMenu.id)}
                  className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-all duration-200 cursor-pointer group"
                >
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg group-hover:bg-blue-100 dark:group-hover:bg-blue-900/30 transition-colors">
                      <Icon size={24} className="text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                        {subMenu.label}
                      </h3>
                    </div>
                    <ChevronRight size={20} className="text-gray-400 group-hover:text-blue-500 transition-colors" />
                  </div>

                  <p className="text-gray-600 dark:text-gray-400">
                    {subMenu.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  }

  // Show the selected component
  return (
    <div className="h-full bg-gray-50 dark:bg-gray-900">
      <div className="h-full flex flex-col">
        {/* Breadcrumb Navigation */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
          <div className="flex items-center space-x-2 text-sm">
            <button
              onClick={() => {
                setActiveSection(null);
                setActiveSubMenu(null);
              }}
              className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
            >
              Administration
            </button>
            <ChevronRight size={16} className="text-gray-400" />
            <button
              onClick={() => setActiveSubMenu(null)}
              className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
            >
              {currentSection?.label}
            </button>
            <ChevronRight size={16} className="text-gray-400" />
            <span className="text-gray-600 dark:text-gray-400">
              {availableSubMenus.find(sm => sm.id === activeSubMenu)?.label}
            </span>
          </div>
        </div>

        {/* Component Content */}
        <div className="flex-1 overflow-auto">
          {CurrentComponent && <CurrentComponent />}
        </div>
      </div>
    </div>
  );
}
