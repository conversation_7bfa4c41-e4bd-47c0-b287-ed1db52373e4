import { Request, Response } from 'express';
import expressValidator from 'express-validator';
const validationResult = (expressValidator as any).validationResult;
import TreatmentPlan from '../models/TreatmentPlan.js';
import Patient from '../models/Patient.js';

// @desc    Get all treatment plans
// @route   GET /api/treatment-plans
// @access  Private
export const getTreatmentPlans = async (req: any, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter: any = {};

    if (req.query.patient) filter.patient = req.query.patient;
    if (req.query.doctor) filter.doctor = req.query.doctor;
    if (req.query.status) filter.status = req.query.status;
    if (req.query.planType) filter.planType = req.query.planType;
    if (req.query.priority) filter.priority = req.query.priority;

    // Date range filter
    if (req.query.startDate || req.query.endDate) {
      filter.startDate = {};
      if (req.query.startDate) filter.startDate.$gte = new Date(req.query.startDate as string);
      if (req.query.endDate) filter.startDate.$lte = new Date(req.query.endDate as string);
    }

    // Search functionality
    if (req.query.search) {
      const searchRegex = new RegExp(req.query.search as string, 'i');
      filter.$or = [
        { planName: searchRegex },
        { 'primaryCondition.diagnosis': searchRegex },
        { planNotes: searchRegex }
      ];
    }

    const treatmentPlans = await TreatmentPlan.find(filter)
      .populate('patient', 'firstName lastName patientId email phone')
      .populate('doctor', 'firstName lastName department')
      .populate('visit', 'visitId visitDate')
      .populate('createdBy', 'firstName lastName')
      .sort({ startDate: -1, createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await TreatmentPlan.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: treatmentPlans,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get treatment plans error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching treatment plans'
    });
  }
};

// @desc    Get single treatment plan
// @route   GET /api/treatment-plans/:id
// @access  Private
export const getTreatmentPlan = async (req: any, res: Response) => {
  try {
    const treatmentPlan = await TreatmentPlan.findById(req.params.id)
      .populate('patient', 'firstName lastName patientId email phone dateOfBirth gender')
      .populate('doctor', 'firstName lastName department email')
      .populate('visit', 'visitId visitDate chiefComplaint')
      .populate('careTeam.member', 'firstName lastName department')
      .populate('createdBy', 'firstName lastName')
      .populate('updatedBy', 'firstName lastName');

    if (!treatmentPlan) {
      return res.status(404).json({
        success: false,
        error: 'Treatment plan not found'
      });
    }

    res.status(200).json({
      success: true,
      data: treatmentPlan
    });
  } catch (error) {
    console.error('Get treatment plan error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching treatment plan'
    });
  }
};

// @desc    Create new treatment plan
// @route   POST /api/treatment-plans
// @access  Private
export const createTreatmentPlan = async (req: any, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    // Verify patient exists
    const patient = await Patient.findById(req.body.patient);
    if (!patient) {
      return res.status(404).json({
        success: false,
        error: 'Patient not found'
      });
    }

    // Add creator information
    req.body.createdBy = req.user.id;

    const treatmentPlan = await TreatmentPlan.create(req.body);

    // Populate the created treatment plan
    const populatedPlan = await TreatmentPlan.findById(treatmentPlan._id)
      .populate('patient', 'firstName lastName patientId')
      .populate('doctor', 'firstName lastName department')
      .populate('createdBy', 'firstName lastName');

    res.status(201).json({
      success: true,
      data: populatedPlan
    });
  } catch (error) {
    console.error('Create treatment plan error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating treatment plan'
    });
  }
};

// @desc    Update treatment plan
// @route   PUT /api/treatment-plans/:id
// @access  Private
export const updateTreatmentPlan = async (req: any, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const treatmentPlan = await TreatmentPlan.findById(req.params.id);

    if (!treatmentPlan) {
      return res.status(404).json({
        success: false,
        error: 'Treatment plan not found'
      });
    }

    // Add updater information
    req.body.updatedBy = req.user.id;

    const updatedPlan = await TreatmentPlan.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    )
      .populate('patient', 'firstName lastName patientId')
      .populate('doctor', 'firstName lastName department')
      .populate('updatedBy', 'firstName lastName');

    res.status(200).json({
      success: true,
      data: updatedPlan
    });
  } catch (error) {
    console.error('Update treatment plan error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating treatment plan'
    });
  }
};

// @desc    Delete treatment plan
// @route   DELETE /api/treatment-plans/:id
// @access  Private
export const deleteTreatmentPlan = async (req: any, res: Response) => {
  try {
    const treatmentPlan = await TreatmentPlan.findById(req.params.id);

    if (!treatmentPlan) {
      return res.status(404).json({
        success: false,
        error: 'Treatment plan not found'
      });
    }

    await TreatmentPlan.findByIdAndDelete(req.params.id);

    res.status(200).json({
      success: true,
      message: 'Treatment plan deleted successfully'
    });
  } catch (error) {
    console.error('Delete treatment plan error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while deleting treatment plan'
    });
  }
};

// @desc    Get patient treatment plans
// @route   GET /api/treatment-plans/patient/:patientId
// @access  Private
export const getPatientTreatmentPlans = async (req: any, res: Response) => {
  try {
    const { patientId } = req.params;
    const status = req.query.status as string;

    // Verify patient exists
    const patient = await Patient.findById(patientId);
    if (!patient) {
      return res.status(404).json({
        success: false,
        error: 'Patient not found'
      });
    }

    const filter: any = { patient: patientId };
    if (status) filter.status = status;

    const treatmentPlans = await TreatmentPlan.find(filter)
      .populate('doctor', 'firstName lastName department')
      .populate('visit', 'visitId visitDate')
      .sort({ startDate: -1 });

    res.status(200).json({
      success: true,
      data: treatmentPlans
    });
  } catch (error) {
    console.error('Get patient treatment plans error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching patient treatment plans'
    });
  }
};

// @desc    Update treatment plan status
// @route   PATCH /api/treatment-plans/:id/status
// @access  Private
export const updateTreatmentPlanStatus = async (req: any, res: Response) => {
  try {
    const { status } = req.body;
    
    if (!status || !['Active', 'Completed', 'Discontinued', 'On Hold', 'Cancelled'].includes(status)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid status provided'
      });
    }

    const treatmentPlan = await TreatmentPlan.findById(req.params.id);

    if (!treatmentPlan) {
      return res.status(404).json({
        success: false,
        error: 'Treatment plan not found'
      });
    }

    treatmentPlan.status = status;
    treatmentPlan.updatedBy = req.user.id;
    
    if (status === 'Completed') {
      treatmentPlan.endDate = new Date();
    }

    await treatmentPlan.save();

    const updatedPlan = await TreatmentPlan.findById(treatmentPlan._id)
      .populate('patient', 'firstName lastName patientId')
      .populate('doctor', 'firstName lastName department');

    res.status(200).json({
      success: true,
      data: updatedPlan,
      message: `Treatment plan status updated to ${status}`
    });
  } catch (error) {
    console.error('Update treatment plan status error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating treatment plan status'
    });
  }
};

// @desc    Add progress note to treatment plan
// @route   POST /api/treatment-plans/:id/progress-notes
// @access  Private
export const addProgressNote = async (req: any, res: Response) => {
  try {
    const { type, content } = req.body;

    if (!type || !content) {
      return res.status(400).json({
        success: false,
        error: 'Type and content are required'
      });
    }

    const treatmentPlan = await TreatmentPlan.findById(req.params.id);

    if (!treatmentPlan) {
      return res.status(404).json({
        success: false,
        error: 'Treatment plan not found'
      });
    }

    const progressNote = {
      date: new Date(),
      author: req.user.id,
      type,
      content,
      attachments: req.body.attachments || []
    };

    treatmentPlan.progressNotes.push(progressNote);
    treatmentPlan.updatedBy = req.user.id;
    await treatmentPlan.save();

    const updatedPlan = await TreatmentPlan.findById(treatmentPlan._id)
      .populate('progressNotes.author', 'firstName lastName')
      .populate('patient', 'firstName lastName patientId');

    res.status(200).json({
      success: true,
      data: updatedPlan,
      message: 'Progress note added successfully'
    });
  } catch (error) {
    console.error('Add progress note error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while adding progress note'
    });
  }
};

// @desc    Get treatment plan statistics
// @route   GET /api/treatment-plans/stats
// @access  Private
export const getTreatmentPlanStats = async (req: any, res: Response) => {
  try {
    const [
      totalPlans,
      activePlans,
      completedPlans,
      onHoldPlans,
      criticalPlans
    ] = await Promise.all([
      TreatmentPlan.countDocuments(),
      TreatmentPlan.countDocuments({ status: 'Active' }),
      TreatmentPlan.countDocuments({ status: 'Completed' }),
      TreatmentPlan.countDocuments({ status: 'On Hold' }),
      TreatmentPlan.countDocuments({ priority: 'Critical', status: 'Active' })
    ]);

    const stats = {
      totalPlans,
      activePlans,
      completedPlans,
      onHoldPlans,
      criticalPlans
    };

    res.status(200).json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get treatment plan stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching treatment plan statistics'
    });
  }
};
