import { Request, Response } from 'express';
import expressValidator from 'express-validator';
const validationResult = (expressValidator as any).validationResult;
import Patient from '../models/Patient.js';
import MedicalRecord from '../models/MedicalRecord.js';
import Appointment from '../models/Appointment.js';

interface AuthRequest extends Request {
  user?: any;
}

// @desc    Get all patients with pagination and filtering
// @route   GET /api/patients
// @access  Private
export const getPatients = async (req: AuthRequest, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter: any = { isActive: true };
    
    if (req.query.status) {
      filter.status = req.query.status;
    }
    
    if (req.query.gender) {
      filter.gender = req.query.gender;
    }
    
    if (req.query.assignedDoctor) {
      filter.assignedDoctor = req.query.assignedDoctor;
    }

    // Search functionality
    if (req.query.search) {
      const searchRegex = new RegExp(req.query.search as string, 'i');
      filter.$or = [
        { firstName: searchRegex },
        { lastName: searchRegex },
        { patientId: searchRegex },
        { email: searchRegex },
        { phone: searchRegex }
      ];
    }

    const patients = await Patient.find(filter)
      .populate('assignedDoctor', 'firstName lastName')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .select('-medicalHistory');

    const total = await Patient.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: patients,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get patients error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching patients'
    });
  }
};

// @desc    Get single patient
// @route   GET /api/patients/:id
// @access  Private
export const getPatient = async (req: AuthRequest, res: Response) => {
  try {
    const patient = await Patient.findById(req.params.id)
      .populate('assignedDoctor', 'firstName lastName email department');

    if (!patient) {
      return res.status(404).json({
        success: false,
        error: 'Patient not found'
      });
    }

    res.status(200).json({
      success: true,
      data: patient
    });
  } catch (error) {
    console.error('Get patient error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching patient'
    });
  }
};

// @desc    Create new patient
// @route   POST /api/patients
// @access  Private
export const createPatient = async (req: AuthRequest, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    // Check if patient with same email or phone already exists
    if (req.body.email) {
      const existingPatient = await Patient.findOne({ 
        email: req.body.email,
        isActive: true 
      });
      
      if (existingPatient) {
        return res.status(400).json({
          success: false,
          error: 'Patient with this email already exists'
        });
      }
    }

    const patient = await Patient.create(req.body);

    res.status(201).json({
      success: true,
      data: patient
    });
  } catch (error) {
    console.error('Create patient error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating patient'
    });
  }
};

// @desc    Update patient
// @route   PUT /api/patients/:id
// @access  Private
export const updatePatient = async (req: AuthRequest, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const patient = await Patient.findById(req.params.id);

    if (!patient) {
      return res.status(404).json({
        success: false,
        error: 'Patient not found'
      });
    }

    // Check if email is being updated and if it already exists
    if (req.body.email && req.body.email !== patient.email) {
      const existingPatient = await Patient.findOne({ 
        email: req.body.email,
        _id: { $ne: req.params.id },
        isActive: true 
      });
      
      if (existingPatient) {
        return res.status(400).json({
          success: false,
          error: 'Patient with this email already exists'
        });
      }
    }

    const updatedPatient = await Patient.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    ).populate('assignedDoctor', 'firstName lastName email department');

    res.status(200).json({
      success: true,
      data: updatedPatient
    });
  } catch (error) {
    console.error('Update patient error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating patient'
    });
  }
};

// @desc    Delete patient (soft delete)
// @route   DELETE /api/patients/:id
// @access  Private
export const deletePatient = async (req: AuthRequest, res: Response) => {
  try {
    const patient = await Patient.findById(req.params.id);

    if (!patient) {
      return res.status(404).json({
        success: false,
        error: 'Patient not found'
      });
    }

    // Soft delete by setting isActive to false
    patient.isActive = false;
    patient.status = 'Inactive';
    await patient.save();

    res.status(200).json({
      success: true,
      message: 'Patient deleted successfully'
    });
  } catch (error) {
    console.error('Delete patient error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while deleting patient'
    });
  }
};

// @desc    Search patients
// @route   GET /api/patients/search
// @access  Private
export const searchPatients = async (req: AuthRequest, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { q } = req.query;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    if (!q) {
      return res.status(400).json({
        success: false,
        error: 'Search query is required'
      });
    }

    const searchRegex = new RegExp(q as string, 'i');
    const filter = {
      isActive: true,
      $or: [
        { firstName: searchRegex },
        { lastName: searchRegex },
        { patientId: searchRegex },
        { email: searchRegex },
        { phone: searchRegex }
      ]
    };

    const patients = await Patient.find(filter)
      .populate('assignedDoctor', 'firstName lastName')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .select('patientId firstName lastName dateOfBirth gender phone email status assignedDoctor');

    const total = await Patient.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: patients,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Search patients error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while searching patients'
    });
  }
};

// @desc    Get patient medical history
// @route   GET /api/patients/:id/medical-history
// @access  Private
export const getPatientMedicalHistory = async (req: AuthRequest, res: Response) => {
  try {
    const patient = await Patient.findById(req.params.id);

    if (!patient) {
      return res.status(404).json({
        success: false,
        error: 'Patient not found'
      });
    }

    const medicalRecords = await MedicalRecord.find({ patient: req.params.id })
      .populate('doctor', 'firstName lastName')
      .populate('appointment')
      .sort({ visitDate: -1 });

    res.status(200).json({
      success: true,
      data: {
        patient: {
          _id: patient._id,
          patientId: patient.patientId,
          fullName: (patient as any).fullName,
          medicalHistory: patient.medicalHistory
        },
        records: medicalRecords
      }
    });
  } catch (error) {
    console.error('Get patient medical history error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching medical history'
    });
  }
};

// @desc    Get patient appointments
// @route   GET /api/patients/:id/appointments
// @access  Private
export const getPatientAppointments = async (req: AuthRequest, res: Response) => {
  try {
    const patient = await Patient.findById(req.params.id);

    if (!patient) {
      return res.status(404).json({
        success: false,
        error: 'Patient not found'
      });
    }

    const appointments = await Appointment.find({ patient: req.params.id })
      .populate('doctor', 'firstName lastName')
      .sort({ appointmentDate: -1 });

    res.status(200).json({
      success: true,
      data: appointments
    });
  } catch (error) {
    console.error('Get patient appointments error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching appointments'
    });
  }
};

// @desc    Admit patient
// @route   POST /api/patients/:id/admit
// @access  Private
export const admitPatient = async (req: AuthRequest, res: Response) => {
  try {
    const { room, assignedDoctor, notes } = req.body;

    const patient = await Patient.findById(req.params.id);

    if (!patient) {
      return res.status(404).json({
        success: false,
        error: 'Patient not found'
      });
    }

    if (patient.status === 'Active' && patient.admissionDate) {
      return res.status(400).json({
        success: false,
        error: 'Patient is already admitted'
      });
    }

    patient.status = 'Active';
    patient.admissionDate = new Date();
    patient.dischargeDate = undefined;
    patient.room = room;
    patient.assignedDoctor = assignedDoctor;
    patient.notes = notes;

    await patient.save();

    const updatedPatient = await Patient.findById(req.params.id)
      .populate('assignedDoctor', 'firstName lastName email department');

    res.status(200).json({
      success: true,
      data: updatedPatient,
      message: 'Patient admitted successfully'
    });
  } catch (error) {
    console.error('Admit patient error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while admitting patient'
    });
  }
};

// @desc    Discharge patient
// @route   POST /api/patients/:id/discharge
// @access  Private
export const dischargePatient = async (req: AuthRequest, res: Response) => {
  try {
    const { notes } = req.body;

    const patient = await Patient.findById(req.params.id);

    if (!patient) {
      return res.status(404).json({
        success: false,
        error: 'Patient not found'
      });
    }

    if (!patient.admissionDate) {
      return res.status(400).json({
        success: false,
        error: 'Patient is not currently admitted'
      });
    }

    patient.dischargeDate = new Date();
    patient.room = undefined;
    patient.notes = notes;

    await patient.save();

    const updatedPatient = await Patient.findById(req.params.id)
      .populate('assignedDoctor', 'firstName lastName email department');

    res.status(200).json({
      success: true,
      data: updatedPatient,
      message: 'Patient discharged successfully'
    });
  } catch (error) {
    console.error('Discharge patient error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while discharging patient'
    });
  }
};
