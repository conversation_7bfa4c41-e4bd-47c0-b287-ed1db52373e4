import { Request, Response } from 'express';
import expressValidator from 'express-validator';
const validationResult = (expressValidator as any).validationResult;
import PatientVisit from '../models/PatientVisit.js';
import Patient from '../models/Patient.js';
import { AuthRequest } from '../middleware/authMiddleware.js';

// @desc    Get all patient visits
// @route   GET /api/patient-visits
// @access  Private
export const getPatientVisits = async (req: AuthRequest, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter: any = {};

    // Filter by patient
    if (req.query.patient) {
      filter.patient = req.query.patient;
    }

    // Filter by doctor
    if (req.query.doctor) {
      filter.doctor = req.query.doctor;
    }

    // Filter by department
    if (req.query.department) {
      filter.department = req.query.department;
    }

    // Filter by visit type
    if (req.query.visitType) {
      filter.visitType = req.query.visitType;
    }

    // Filter by status
    if (req.query.status) {
      filter.status = req.query.status;
    }

    // Filter by date range
    if (req.query.startDate || req.query.endDate) {
      filter.visitDate = {};
      if (req.query.startDate) {
        filter.visitDate.$gte = new Date(req.query.startDate as string);
      }
      if (req.query.endDate) {
        filter.visitDate.$lte = new Date(req.query.endDate as string);
      }
    }

    // Search functionality
    if (req.query.search) {
      const searchRegex = new RegExp(req.query.search as string, 'i');
      filter.$or = [
        { visitId: searchRegex },
        { chiefComplaint: searchRegex },
        { 'assessment.primaryDiagnosis.description': searchRegex }
      ];
    }

    const visits = await PatientVisit.find(filter)
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('doctor', 'firstName lastName department')
      .sort({ visitDate: -1, visitTime: -1 })
      .skip(skip)
      .limit(limit);

    const total = await PatientVisit.countDocuments(filter);

    res.json({
      success: true,
      data: visits,
      pagination: {
        total,
        page: parseInt(String(page)),
        limit: parseInt(String(limit)),
        pages: Math.ceil(total / parseInt(String(limit)))
      }
    });
  } catch (error) {
    console.error('Error fetching patient visits:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch patient visits'
    });
  }
};

// @desc    Get single patient visit
// @route   GET /api/patient-visits/:id
// @access  Private
export const getPatientVisitById = async (req: AuthRequest, res: Response) => {
  try {
    const visit = await PatientVisit.findById(req.params.id)
      .populate('patient', 'firstName lastName patientId phone email dateOfBirth gender')
      .populate('doctor', 'firstName lastName department specialization')
      .populate('createdBy', 'firstName lastName');

    if (!visit) {
      return res.status(404).json({
        success: false,
        error: 'Patient visit not found'
      });
    }

    res.json({
      success: true,
      data: visit
    });
  } catch (error) {
    console.error('Error fetching patient visit:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch patient visit'
    });
  }
};

// @desc    Create new patient visit
// @route   POST /api/patient-visits
// @access  Private
export const createPatientVisit = async (req: AuthRequest, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    // Verify patient exists
    const patient = await Patient.findById(req.body.patient);
    if (!patient) {
      return res.status(404).json({
        success: false,
        error: 'Patient not found'
      });
    }

    // Add creator information
    req.body.createdBy = req.user.id;

    const visitData = {
      ...req.body,
      createdBy: req.user?.id || req.body.createdBy
    };

    // Generate visit ID if not provided
    if (!visitData.visitId) {
      const count = await PatientVisit.countDocuments();
      visitData.visitId = `VIS${String(count + 1).padStart(6, '0')}`;
    }

    const visit = new PatientVisit(visitData);
    await visit.save();

    // Populate the created visit
    const populatedVisit = await PatientVisit.findById(visit._id)
      .populate('patient', 'firstName lastName patientId')
      .populate('doctor', 'firstName lastName department');

    res.status(201).json({
      success: true,
      data: populatedVisit
    });
  } catch (error) {
    console.error('Error creating patient visit:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create patient visit'
    });
  }
};

// @desc    Update patient visit
// @route   PUT /api/patient-visits/:id
// @access  Private
export const updatePatientVisit = async (req: AuthRequest, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const visit = await PatientVisit.findById(req.params.id);

    if (!visit) {
      return res.status(404).json({
        success: false,
        error: 'Patient visit not found'
      });
    }

    // Add updater information
    req.body.updatedBy = req.user.id;

    const updatedVisit = await PatientVisit.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedAt: new Date() },
      { new: true, runValidators: true }
    )
      .populate('patient', 'firstName lastName patientId')
      .populate('doctor', 'firstName lastName department');

    res.json({
      success: true,
      data: updatedVisit
    });
  } catch (error) {
    console.error('Error updating patient visit:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update patient visit'
    });
  }
};

// @desc    Delete patient visit
// @route   DELETE /api/patient-visits/:id
// @access  Private
export const deletePatientVisit = async (req: AuthRequest, res: Response) => {
  try {
    const visit = await PatientVisit.findById(req.params.id);

    if (!visit) {
      return res.status(404).json({
        success: false,
        error: 'Patient visit not found'
      });
    }

    await PatientVisit.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: 'Patient visit deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting patient visit:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete patient visit'
    });
  }
};

// @desc    Get patient visit history
// @route   GET /api/patient-visits/patient/:patientId/history
// @access  Private
export const getPatientVisitHistory = async (req: AuthRequest, res: Response) => {
  try {
    const { patientId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    // Verify patient exists
    const patient = await Patient.findById(patientId);
    if (!patient) {
      return res.status(404).json({
        success: false,
        error: 'Patient not found'
      });
    }

    const visits = await PatientVisit.find({ patient: patientId })
      .populate('doctor', 'firstName lastName department')
      .populate('appointment', 'appointmentId appointmentDate appointmentTime')
      .sort({ visitDate: -1, createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await PatientVisit.countDocuments({ patient: patientId });

    res.status(200).json({
      success: true,
      data: visits,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get patient visit history error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching patient visit history'
    });
  }
};

// @desc    Get visit statistics
// @route   GET /api/patient-visits/stats
// @access  Private
export const getPatientVisitStats = async (req: AuthRequest, res: Response) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const [
      totalVisits,
      todayVisits,
      inProgressVisits,
      completedVisits,
      avgDuration
    ] = await Promise.all([
      PatientVisit.countDocuments(),
      PatientVisit.countDocuments({
        visitDate: { $gte: today, $lt: tomorrow }
      }),
      PatientVisit.countDocuments({ status: 'In Progress' }),
      PatientVisit.countDocuments({ status: 'Completed' }),
      PatientVisit.aggregate([
        { $match: { status: 'Completed', visitDuration: { $exists: true } } },
        { $group: { _id: null, avgDuration: { $avg: '$visitDuration' } } }
      ])
    ]);

    const stats = {
      totalVisits,
      todayVisits,
      inProgressVisits,
      completedVisits,
      averageVisitDuration: avgDuration.length > 0 ? Math.round(avgDuration[0].avgDuration) : 0
    };

    res.status(200).json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get visit stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching visit statistics'
    });
  }
};

// @desc    Update visit status
// @route   PATCH /api/patient-visits/:id/status
// @access  Private
export const updatePatientVisitStatus = async (req: AuthRequest, res: Response) => {
  try {
    const { status } = req.body;
    
    if (!status || !['Scheduled', 'In Progress', 'Completed', 'Cancelled', 'No Show'].includes(status)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid status provided'
      });
    }

    const visit = await PatientVisit.findById(req.params.id);

    if (!visit) {
      return res.status(404).json({
        success: false,
        error: 'Patient visit not found'
      });
    }

    visit.status = status;
    visit.updatedBy = req.user.id;
    await visit.save();

    const updatedVisit = await PatientVisit.findById(visit._id)
      .populate('patient', 'firstName lastName patientId')
      .populate('doctor', 'firstName lastName department');

    res.status(200).json({
      success: true,
      data: updatedVisit,
      message: `Visit status updated to ${status}`
    });
  } catch (error) {
    console.error('Update visit status error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating visit status'
    });
  }
};
