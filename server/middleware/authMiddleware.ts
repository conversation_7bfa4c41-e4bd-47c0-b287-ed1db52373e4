import jwt from 'jsonwebtoken';
import { Request, Response, NextFunction } from 'express';
import User from '../models/User.js';

export interface AuthRequest extends Request {
  user?: any;
}

export const protect = async (req: AuthRequest, res: Response, next: NextFunction) => {
  let token;

  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    try {
      // Get token from header
      token = req.headers.authorization.split(' ')[1];

      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback_secret') as jwt.JwtPayload;

      // Get user from token
      req.user = await User.findById(decoded.id)
        .populate('role')
        .populate('permissions')
        .select('-password');

      if (!req.user) {
        return res.status(401).json({ success: false, error: 'User not found' });
      }

      if (!req.user.isActive) {
        return res.status(401).json({ success: false, error: 'Account is deactivated' });
      }

      next();
    } catch (error) {
      console.error('Auth middleware error:', error);
      return res.status(401).json({ success: false, error: 'Not authorized, token failed' });
    }
  }

  if (!token) {
    return res.status(401).json({ success: false, error: 'Not authorized, no token' });
  }
};

export const authorize = (...roles: string[]) => {
  return (req: AuthRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ success: false, error: 'User not authenticated' });
    }

    if (!roles.includes(req.user.role.name)) {
      return res.status(403).json({ 
        success: false, 
        error: `User role ${req.user.role.name} is not authorized to access this route` 
      });
    }

    next();
  };
};

export const checkPermission = (module: string, action: string, resource?: string) => {
  return (req: AuthRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ success: false, error: 'User not authenticated' });
    }

    // Super admin has all permissions
    if (req.user.role.level === 10) {
      return next();
    }

    // Check if user has the required permission
    const hasPermission = req.user.permissions.some((permission: any) =>
      permission.module === module &&
      permission.action === action &&
      (!resource || permission.resource === resource || permission.resource === '*')
    );

    if (!hasPermission) {
      return res.status(403).json({ 
        success: false, 
        error: `Insufficient permissions for ${action} on ${module}${resource ? `/${resource}` : ''}` 
      });
    }

    next();
  };
};
