{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "outDir": "../dist/server", "rootDir": ".", "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "declaration": false}, "include": ["**/*.ts", "**/*.js"], "exclude": ["node_modules", "dist"]}