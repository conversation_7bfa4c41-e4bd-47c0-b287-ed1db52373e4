import mongoose, { Schema, Document } from 'mongoose';

export interface IPatientVisit extends Document {
  visitId: string;
  patient: mongoose.Types.ObjectId;
  doctor: mongoose.Types.ObjectId;
  appointment?: mongoose.Types.ObjectId;
  visitDate: Date;
  visitTime: string;
  visitType: 'Consultation' | 'Follow-up' | 'Emergency' | 'Routine Check-up' | 'Procedure' | 'Vaccination' | 'Screening';
  department: string;
  chiefComplaint: string;
  historyOfPresentIllness: string;
  reviewOfSystems: {
    constitutional: string;
    cardiovascular: string;
    respiratory: string;
    gastrointestinal: string;
    genitourinary: string;
    musculoskeletal: string;
    neurological: string;
    psychiatric: string;
    endocrine: string;
    hematologic: string;
    allergic: string;
  };
  physicalExamination: {
    vitalSigns: {
      temperature: number;
      bloodPressure: {
        systolic: number;
        diastolic: number;
      };
      heartRate: number;
      respiratoryRate: number;
      oxygenSaturation: number;
      weight: number;
      height: number;
      bmi?: number;
      painScale?: number;
    };
    generalAppearance: string;
    heent: string; // Head, Eyes, Ears, Nose, Throat
    cardiovascular: string;
    respiratory: string;
    abdominal: string;
    musculoskeletal: string;
    neurological: string;
    skin: string;
    psychiatric: string;
  };
  assessment: {
    primaryDiagnosis: {
      code: string;
      description: string;
      icdCode?: string;
    };
    secondaryDiagnoses: Array<{
      code: string;
      description: string;
      icdCode?: string;
    }>;
    differentialDiagnoses: Array<{
      code: string;
      description: string;
      probability: 'High' | 'Medium' | 'Low';
    }>;
  };
  plan: {
    medications: Array<{
      name: string;
      dosage: string;
      frequency: string;
      duration: string;
      route: 'Oral' | 'IV' | 'IM' | 'Topical' | 'Inhaled' | 'Sublingual' | 'Other';
      instructions: string;
      quantity?: number;
      refills?: number;
    }>;
    procedures: Array<{
      name: string;
      scheduledDate?: Date;
      urgency: 'Routine' | 'Urgent' | 'STAT';
      instructions?: string;
      department?: string;
    }>;
    labOrders: Array<{
      testName: string;
      urgency: 'Routine' | 'Urgent' | 'STAT';
      instructions?: string;
      fastingRequired?: boolean;
    }>;
    imagingOrders: Array<{
      type: string;
      bodyPart: string;
      urgency: 'Routine' | 'Urgent' | 'STAT';
      contrast?: boolean;
      instructions?: string;
    }>;
    referrals: Array<{
      specialty: string;
      doctor?: string;
      urgency: 'Routine' | 'Urgent' | 'STAT';
      reason: string;
      instructions?: string;
    }>;
    followUp: {
      required: boolean;
      timeframe?: string;
      instructions?: string;
      appointmentType?: string;
    };
    patientEducation: Array<{
      topic: string;
      materials?: string;
      instructions: string;
    }>;
  };
  visitNotes: string;
  privateNotes?: string;
  visitDuration: number; // in minutes
  status: 'Scheduled' | 'In Progress' | 'Completed' | 'Cancelled' | 'No Show';
  billingCodes: Array<{
    code: string;
    description: string;
    amount?: number;
  }>;
  attachments: Array<{
    fileName: string;
    fileType: string;
    fileUrl: string;
    uploadedAt: Date;
    uploadedBy: mongoose.Types.ObjectId;
  }>;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
}

const PatientVisitSchema: Schema = new Schema({
  visitId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'VIS' + Date.now().toString().slice(-6);
    }
  },
  patient: {
    type: Schema.Types.ObjectId,
    ref: 'Patient',
    required: true,
    index: true
  },
  doctor: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  appointment: {
    type: Schema.Types.ObjectId,
    ref: 'Appointment'
  },
  visitDate: {
    type: Date,
    required: true,
    index: true
  },
  visitTime: {
    type: String,
    required: true
  },
  visitType: {
    type: String,
    required: true,
    enum: ['Consultation', 'Follow-up', 'Emergency', 'Routine Check-up', 'Procedure', 'Vaccination', 'Screening'],
    index: true
  },
  department: {
    type: String,
    required: true,
    enum: [
      'Cardiology', 'Neurology', 'Orthopedics', 'Pediatrics', 'Gynecology',
      'Dermatology', 'Psychiatry', 'Oncology', 'Emergency', 'General Medicine',
      'Surgery', 'Radiology', 'Pathology', 'Anesthesiology', 'Ophthalmology',
      'ENT', 'Urology', 'Gastroenterology', 'Pulmonology', 'Nephrology'
    ]
  },
  chiefComplaint: {
    type: String,
    required: true,
    trim: true
  },
  historyOfPresentIllness: {
    type: String,
    required: true,
    trim: true
  },
  reviewOfSystems: {
    constitutional: String,
    cardiovascular: String,
    respiratory: String,
    gastrointestinal: String,
    genitourinary: String,
    musculoskeletal: String,
    neurological: String,
    psychiatric: String,
    endocrine: String,
    hematologic: String,
    allergic: String
  },
  physicalExamination: {
    vitalSigns: {
      temperature: { type: Number, min: 90, max: 110 },
      bloodPressure: {
        systolic: { type: Number, min: 60, max: 250 },
        diastolic: { type: Number, min: 40, max: 150 }
      },
      heartRate: { type: Number, min: 30, max: 200 },
      respiratoryRate: { type: Number, min: 8, max: 40 },
      oxygenSaturation: { type: Number, min: 70, max: 100 },
      weight: { type: Number, min: 0, max: 1000 },
      height: { type: Number, min: 0, max: 300 },
      bmi: Number,
      painScale: { type: Number, min: 0, max: 10 }
    },
    generalAppearance: String,
    heent: String,
    cardiovascular: String,
    respiratory: String,
    abdominal: String,
    musculoskeletal: String,
    neurological: String,
    skin: String,
    psychiatric: String
  },
  assessment: {
    primaryDiagnosis: {
      code: { type: String, required: true },
      description: { type: String, required: true },
      icdCode: String
    },
    secondaryDiagnoses: [{
      code: { type: String, required: true },
      description: { type: String, required: true },
      icdCode: String
    }],
    differentialDiagnoses: [{
      code: { type: String, required: true },
      description: { type: String, required: true },
      probability: {
        type: String,
        enum: ['High', 'Medium', 'Low'],
        default: 'Medium'
      }
    }]
  },
  plan: {
    medications: [{
      name: { type: String, required: true },
      dosage: { type: String, required: true },
      frequency: { type: String, required: true },
      duration: { type: String, required: true },
      route: {
        type: String,
        enum: ['Oral', 'IV', 'IM', 'Topical', 'Inhaled', 'Sublingual', 'Other'],
        default: 'Oral'
      },
      instructions: { type: String, required: true },
      quantity: Number,
      refills: { type: Number, default: 0 }
    }],
    procedures: [{
      name: { type: String, required: true },
      scheduledDate: Date,
      urgency: {
        type: String,
        enum: ['Routine', 'Urgent', 'STAT'],
        default: 'Routine'
      },
      instructions: String,
      department: String
    }],
    labOrders: [{
      testName: { type: String, required: true },
      urgency: {
        type: String,
        enum: ['Routine', 'Urgent', 'STAT'],
        default: 'Routine'
      },
      instructions: String,
      fastingRequired: { type: Boolean, default: false }
    }],
    imagingOrders: [{
      type: { type: String, required: true },
      bodyPart: { type: String, required: true },
      urgency: {
        type: String,
        enum: ['Routine', 'Urgent', 'STAT'],
        default: 'Routine'
      },
      contrast: { type: Boolean, default: false },
      instructions: String
    }],
    referrals: [{
      specialty: { type: String, required: true },
      doctor: String,
      urgency: {
        type: String,
        enum: ['Routine', 'Urgent', 'STAT'],
        default: 'Routine'
      },
      reason: { type: String, required: true },
      instructions: String
    }],
    followUp: {
      required: { type: Boolean, default: false },
      timeframe: String,
      instructions: String,
      appointmentType: String
    },
    patientEducation: [{
      topic: { type: String, required: true },
      materials: String,
      instructions: { type: String, required: true }
    }]
  },
  visitNotes: {
    type: String,
    required: true,
    trim: true
  },
  privateNotes: {
    type: String,
    trim: true
  },
  visitDuration: {
    type: Number,
    default: 30,
    min: 5,
    max: 480
  },
  status: {
    type: String,
    required: true,
    enum: ['Scheduled', 'In Progress', 'Completed', 'Cancelled', 'No Show'],
    default: 'Scheduled',
    index: true
  },
  billingCodes: [{
    code: { type: String, required: true },
    description: { type: String, required: true },
    amount: Number
  }],
  attachments: [{
    fileName: { type: String, required: true },
    fileType: { type: String, required: true },
    fileUrl: { type: String, required: true },
    uploadedAt: { type: Date, default: Date.now },
    uploadedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    }
  }],
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes for better performance
PatientVisitSchema.index({ visitId: 1 });
PatientVisitSchema.index({ patient: 1, visitDate: -1 });
PatientVisitSchema.index({ doctor: 1, visitDate: -1 });
PatientVisitSchema.index({ department: 1, visitDate: -1 });
PatientVisitSchema.index({ visitType: 1, visitDate: -1 });
PatientVisitSchema.index({ status: 1 });

// Compound indexes
PatientVisitSchema.index({ patient: 1, status: 1, visitDate: -1 });
PatientVisitSchema.index({ doctor: 1, status: 1, visitDate: -1 });

// Virtual for visit duration in hours
PatientVisitSchema.virtual('visitDurationHours').get(function() {
  return (this.visitDuration as any) / 60;
});

// Pre-save middleware to calculate BMI
PatientVisitSchema.pre('save', function(next) {
  if (this.physicalExamination?.vitalSigns?.weight && this.physicalExamination?.vitalSigns?.height) {
    const heightInMeters = this.physicalExamination.vitalSigns.height / 100;
    this.physicalExamination.vitalSigns.bmi = Number((this.physicalExamination.vitalSigns.weight / (heightInMeters * heightInMeters)).toFixed(1));
  }
  next();
});

export default mongoose.model<IPatientVisit>('PatientVisit', PatientVisitSchema);
