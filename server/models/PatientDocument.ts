import mongoose, { Schema, Document } from 'mongoose';

export interface IPatientDocument extends Document {
  documentId: string;
  patient: mongoose.Types.ObjectId;
  visit?: mongoose.Types.ObjectId;
  appointment?: mongoose.Types.ObjectId;
  treatmentPlan?: mongoose.Types.ObjectId;
  
  // Document metadata
  documentName: string;
  documentType: 'Medical Report' | 'Lab Result' | 'Imaging Study' | 'Prescription' | 'Consent Form' | 
                'Insurance Document' | 'Referral Letter' | 'Discharge Summary' | 'Progress Note' | 
                'Vaccination Record' | 'Allergy Alert' | 'Emergency Contact' | 'Other';
  category: 'Clinical' | 'Administrative' | 'Legal' | 'Insurance' | 'Personal';
  subCategory?: string;
  
  // File information
  fileName: string;
  originalFileName: string;
  fileSize: number; // in bytes
  fileType: string; // MIME type
  fileExtension: string;
  fileUrl: string;
  thumbnailUrl?: string;
  
  // Document content and metadata
  description?: string;
  keywords: string[];
  tags: string[];
  
  // Clinical relevance
  clinicalRelevance: 'High' | 'Medium' | 'Low';
  isConfidential: boolean;
  isCritical: boolean;
  
  // Document dates
  documentDate: Date; // Date the document was created/issued
  expiryDate?: Date;
  
  // Source information
  sourceType: 'Internal' | 'External' | 'Patient Uploaded' | 'System Generated';
  sourceDetails?: {
    institution?: string;
    department?: string;
    provider?: string;
    system?: string;
  };
  
  // Document creator/uploader
  uploadedBy: mongoose.Types.ObjectId;
  createdBy?: mongoose.Types.ObjectId; // Original document creator (if different from uploader)
  
  // Review and approval
  reviewStatus: 'Pending' | 'Reviewed' | 'Approved' | 'Rejected' | 'Requires Update';
  reviewedBy?: mongoose.Types.ObjectId;
  reviewedAt?: Date;
  reviewNotes?: string;
  
  // Access control
  accessLevel: 'Public' | 'Restricted' | 'Confidential' | 'Highly Confidential';
  sharedWith: Array<{
    user: mongoose.Types.ObjectId;
    role: string;
    permissions: string[];
    sharedAt: Date;
    sharedBy: mongoose.Types.ObjectId;
  }>;
  
  // Document versioning
  version: number;
  parentDocument?: mongoose.Types.ObjectId;
  isLatestVersion: boolean;
  versionNotes?: string;
  
  // Document processing
  isProcessed: boolean;
  processingStatus: 'Pending' | 'Processing' | 'Completed' | 'Failed';
  extractedText?: string;
  ocrConfidence?: number;
  
  // Lab/Imaging specific fields
  labResults?: Array<{
    testName: string;
    result: string;
    normalRange: string;
    unit: string;
    status: 'Normal' | 'Abnormal' | 'Critical';
    flag?: string;
  }>;
  
  imagingResults?: {
    studyType: string;
    bodyPart: string;
    findings: string;
    impression: string;
    radiologist?: mongoose.Types.ObjectId;
    technique?: string;
    contrast?: boolean;
  };
  
  // Prescription specific fields
  prescriptionDetails?: {
    medications: Array<{
      name: string;
      dosage: string;
      frequency: string;
      duration: string;
      quantity: number;
      refills: number;
    }>;
    prescribedBy: mongoose.Types.ObjectId;
    pharmacy?: string;
    instructions?: string;
  };
  
  // Consent form specific fields
  consentDetails?: {
    consentType: string;
    procedure?: string;
    risks: string[];
    benefits: string[];
    alternatives: string[];
    patientSignature: boolean;
    witnessSignature: boolean;
    guardianSignature?: boolean;
    consentDate: Date;
  };
  
  // Document relationships
  relatedDocuments: Array<{
    document: mongoose.Types.ObjectId;
    relationship: 'Supersedes' | 'Superseded By' | 'Related To' | 'Amendment To' | 'Copy Of';
    notes?: string;
  }>;
  
  // Audit trail
  auditTrail: Array<{
    action: 'Created' | 'Viewed' | 'Downloaded' | 'Updated' | 'Shared' | 'Deleted' | 'Restored';
    performedBy: mongoose.Types.ObjectId;
    timestamp: Date;
    ipAddress?: string;
    userAgent?: string;
    details?: string;
  }>;
  
  // Document status
  status: 'Active' | 'Archived' | 'Deleted' | 'Expired';
  isActive: boolean;
  
  // Compliance and legal
  retentionPeriod?: number; // in years
  legalHold: boolean;
  complianceFlags: string[];
  
  // Search and indexing
  searchableContent: string;
  indexedAt?: Date;
  
  // Metadata
  customFields?: Array<{
    fieldName: string;
    fieldValue: string;
    fieldType: 'text' | 'number' | 'date' | 'boolean';
  }>;
  
  notes?: string;
}

const PatientDocumentSchema: Schema = new Schema({
  documentId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'DOC' + Date.now().toString().slice(-6);
    }
  },
  patient: {
    type: Schema.Types.ObjectId,
    ref: 'Patient',
    required: true,
    index: true
  },
  visit: {
    type: Schema.Types.ObjectId,
    ref: 'PatientVisit'
  },
  appointment: {
    type: Schema.Types.ObjectId,
    ref: 'Appointment'
  },
  treatmentPlan: {
    type: Schema.Types.ObjectId,
    ref: 'TreatmentPlan'
  },
  documentName: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  documentType: {
    type: String,
    required: true,
    enum: [
      'Medical Report', 'Lab Result', 'Imaging Study', 'Prescription', 'Consent Form',
      'Insurance Document', 'Referral Letter', 'Discharge Summary', 'Progress Note',
      'Vaccination Record', 'Allergy Alert', 'Emergency Contact', 'Other'
    ],
    index: true
  },
  category: {
    type: String,
    required: true,
    enum: ['Clinical', 'Administrative', 'Legal', 'Insurance', 'Personal'],
    index: true
  },
  subCategory: {
    type: String,
    trim: true
  },
  fileName: {
    type: String,
    required: true
  },
  originalFileName: {
    type: String,
    required: true
  },
  fileSize: {
    type: Number,
    required: true,
    min: 0
  },
  fileType: {
    type: String,
    required: true
  },
  fileExtension: {
    type: String,
    required: true
  },
  fileUrl: {
    type: String,
    required: true
  },
  thumbnailUrl: String,
  description: {
    type: String,
    trim: true
  },
  keywords: {
    type: [String],
    index: true
  },
  tags: {
    type: [String],
    index: true
  },
  clinicalRelevance: {
    type: String,
    enum: ['High', 'Medium', 'Low'],
    default: 'Medium',
    index: true
  },
  isConfidential: {
    type: Boolean,
    default: false,
    index: true
  },
  isCritical: {
    type: Boolean,
    default: false,
    index: true
  },
  documentDate: {
    type: Date,
    required: true,
    index: true
  },
  expiryDate: {
    type: Date,
    index: true
  },
  sourceType: {
    type: String,
    required: true,
    enum: ['Internal', 'External', 'Patient Uploaded', 'System Generated'],
    index: true
  },
  sourceDetails: {
    institution: String,
    department: String,
    provider: String,
    system: String
  },
  uploadedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  reviewStatus: {
    type: String,
    enum: ['Pending', 'Reviewed', 'Approved', 'Rejected', 'Requires Update'],
    default: 'Pending',
    index: true
  },
  reviewedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  reviewedAt: Date,
  reviewNotes: {
    type: String,
    trim: true
  },
  accessLevel: {
    type: String,
    required: true,
    enum: ['Public', 'Restricted', 'Confidential', 'Highly Confidential'],
    default: 'Restricted',
    index: true
  },
  sharedWith: [{
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    role: { type: String, required: true },
    permissions: [String],
    sharedAt: { type: Date, default: Date.now },
    sharedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    }
  }],
  version: {
    type: Number,
    default: 1,
    min: 1
  },
  parentDocument: {
    type: Schema.Types.ObjectId,
    ref: 'PatientDocument'
  },
  isLatestVersion: {
    type: Boolean,
    default: true,
    index: true
  },
  versionNotes: {
    type: String,
    trim: true
  },
  isProcessed: {
    type: Boolean,
    default: false,
    index: true
  },
  processingStatus: {
    type: String,
    enum: ['Pending', 'Processing', 'Completed', 'Failed'],
    default: 'Pending',
    index: true
  },
  extractedText: {
    type: String,
    trim: true
  },
  ocrConfidence: {
    type: Number,
    min: 0,
    max: 100
  },
  labResults: [{
    testName: { type: String, required: true },
    result: { type: String, required: true },
    normalRange: String,
    unit: String,
    status: {
      type: String,
      enum: ['Normal', 'Abnormal', 'Critical'],
      required: true
    },
    flag: String
  }],
  imagingResults: {
    studyType: String,
    bodyPart: String,
    findings: String,
    impression: String,
    radiologist: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    technique: String,
    contrast: Boolean
  },
  prescriptionDetails: {
    medications: [{
      name: { type: String, required: true },
      dosage: { type: String, required: true },
      frequency: { type: String, required: true },
      duration: { type: String, required: true },
      quantity: { type: Number, required: true },
      refills: { type: Number, default: 0 }
    }],
    prescribedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    pharmacy: String,
    instructions: String
  },
  consentDetails: {
    consentType: String,
    procedure: String,
    risks: [String],
    benefits: [String],
    alternatives: [String],
    patientSignature: Boolean,
    witnessSignature: Boolean,
    guardianSignature: Boolean,
    consentDate: Date
  },
  relatedDocuments: [{
    document: {
      type: Schema.Types.ObjectId,
      ref: 'PatientDocument',
      required: true
    },
    relationship: {
      type: String,
      enum: ['Supersedes', 'Superseded By', 'Related To', 'Amendment To', 'Copy Of'],
      required: true
    },
    notes: String
  }],
  auditTrail: [{
    action: {
      type: String,
      enum: ['Created', 'Viewed', 'Downloaded', 'Updated', 'Shared', 'Deleted', 'Restored'],
      required: true
    },
    performedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    timestamp: { type: Date, default: Date.now },
    ipAddress: String,
    userAgent: String,
    details: String
  }],
  status: {
    type: String,
    enum: ['Active', 'Archived', 'Deleted', 'Expired'],
    default: 'Active',
    index: true
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  retentionPeriod: {
    type: Number,
    min: 1
  },
  legalHold: {
    type: Boolean,
    default: false,
    index: true
  },
  complianceFlags: [String],
  searchableContent: {
    type: String,
    trim: true
  },
  indexedAt: Date,
  customFields: [{
    fieldName: { type: String, required: true },
    fieldValue: { type: String, required: true },
    fieldType: {
      type: String,
      enum: ['text', 'number', 'date', 'boolean'],
      default: 'text'
    }
  }],
  notes: {
    type: String,
    trim: true
  }
}, {
  timestamps: true
});

// Indexes for better performance
PatientDocumentSchema.index({ documentId: 1 });
PatientDocumentSchema.index({ patient: 1, documentDate: -1 });
PatientDocumentSchema.index({ patient: 1, documentType: 1 });
PatientDocumentSchema.index({ patient: 1, category: 1 });
PatientDocumentSchema.index({ documentType: 1, documentDate: -1 });
PatientDocumentSchema.index({ category: 1, documentDate: -1 });
PatientDocumentSchema.index({ uploadedBy: 1, createdAt: -1 });
PatientDocumentSchema.index({ reviewStatus: 1 });
PatientDocumentSchema.index({ accessLevel: 1 });
PatientDocumentSchema.index({ status: 1, isActive: 1 });

// Text index for search functionality
PatientDocumentSchema.index({
  documentName: 'text',
  description: 'text',
  keywords: 'text',
  tags: 'text',
  searchableContent: 'text'
});

// Compound indexes
PatientDocumentSchema.index({ patient: 1, status: 1, documentDate: -1 });
PatientDocumentSchema.index({ patient: 1, isActive: 1, documentDate: -1 });

// Virtual for file size in MB
PatientDocumentSchema.virtual('fileSizeMB').get(function() {
  return ((this.fileSize as any) / (1024 * 1024)).toFixed(2);
});

// Pre-save middleware to update searchable content
PatientDocumentSchema.pre('save', function(next) {
  // Combine searchable fields
  const searchableFields = [
    this.documentName,
    this.description,
    this.keywords?.join(' '),
    this.tags?.join(' '),
    this.extractedText
  ].filter(Boolean);
  
  this.searchableContent = searchableFields.join(' ').toLowerCase();
  next();
});

export default mongoose.model<IPatientDocument>('PatientDocument', PatientDocumentSchema);
