import mongoose, { Schema, Document } from 'mongoose';

export interface IBill extends Document {
  billId: string;
  patient: mongoose.Types.ObjectId;
  appointment?: mongoose.Types.ObjectId;
  billDate: Date;
  dueDate: Date;
  items: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    category: 'Consultation' | 'Procedure' | 'Medication' | 'Laboratory' | 'Imaging' | 'Room Charges' | 'Other';
  }>;
  subtotal: number;
  tax: number;
  discount: number;
  totalAmount: number;
  paidAmount: number;
  balanceAmount: number;
  paymentStatus: 'Pending' | 'Partial' | 'Paid' | 'Overdue' | 'Cancelled';
  paymentMethod?: 'Cash' | 'Credit Card' | 'Debit Card' | 'Insurance' | 'Bank Transfer' | 'Check';
  insurance?: {
    provider: string;
    policyNumber: string;
    claimNumber?: string;
    approvedAmount?: number;
    status: 'Pending' | 'Approved' | 'Rejected' | 'Partial';
  };
  payments: Array<{
    date: Date;
    amount: number;
    method: string;
    reference?: string;
    receivedBy: mongoose.Types.ObjectId;
  }>;
  notes?: string;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
}

const BillSchema: Schema = new Schema({
  billId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'BILL' + Date.now().toString().slice(-6);
    }
  },
  patient: {
    type: Schema.Types.ObjectId,
    ref: 'Patient',
    required: true,
    index: true
  },
  appointment: {
    type: Schema.Types.ObjectId,
    ref: 'Appointment'
  },
  billDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  dueDate: {
    type: Date,
    required: true,
    default: function() {
      return new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days from now
    }
  },
  items: [{
    description: { type: String, required: true },
    quantity: { type: Number, required: true, min: 0 },
    unitPrice: { type: Number, required: true, min: 0 },
    totalPrice: { type: Number, required: true, min: 0 },
    category: {
      type: String,
      required: true,
      enum: ['Consultation', 'Procedure', 'Medication', 'Laboratory', 'Imaging', 'Room Charges', 'Other']
    }
  }],
  subtotal: {
    type: Number,
    required: true,
    min: 0
  },
  tax: {
    type: Number,
    default: 0,
    min: 0
  },
  discount: {
    type: Number,
    default: 0,
    min: 0
  },
  totalAmount: {
    type: Number,
    required: true,
    min: 0
  },
  paidAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  balanceAmount: {
    type: Number,
    required: true,
    min: 0
  },
  paymentStatus: {
    type: String,
    required: true,
    enum: ['Pending', 'Partial', 'Paid', 'Overdue', 'Cancelled'],
    default: 'Pending',
    index: true
  },
  paymentMethod: {
    type: String,
    enum: ['Cash', 'Credit Card', 'Debit Card', 'Insurance', 'Bank Transfer', 'Check']
  },
  insurance: {
    provider: String,
    policyNumber: String,
    claimNumber: String,
    approvedAmount: { type: Number, min: 0 },
    status: {
      type: String,
      enum: ['Pending', 'Approved', 'Rejected', 'Partial'],
      default: 'Pending'
    }
  },
  payments: [{
    date: { type: Date, required: true },
    amount: { type: Number, required: true, min: 0 },
    method: { type: String, required: true },
    reference: String,
    receivedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    }
  }],
  notes: {
    type: String,
    trim: true
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes
BillSchema.index({ billId: 1 });
BillSchema.index({ patient: 1 });
BillSchema.index({ billDate: 1 });
BillSchema.index({ paymentStatus: 1 });
BillSchema.index({ dueDate: 1 });

// Pre-save middleware to calculate totals
BillSchema.pre('save', function(next) {
  // Calculate subtotal
  this.subtotal = (this.items as any).reduce((sum: any, item: any) => sum + item.totalPrice, 0);
  
  // Calculate total amount
  this.totalAmount = this.subtotal + this.tax - this.discount;
  
  // Calculate balance amount
  this.balanceAmount = this.totalAmount - this.paidAmount;
  
  // Update payment status based on balance
  if (this.balanceAmount <= 0) {
    this.paymentStatus = 'Paid';
  } else if (this.paidAmount > 0) {
    this.paymentStatus = 'Partial';
  } else if (new Date() > this.dueDate) {
    this.paymentStatus = 'Overdue';
  } else {
    this.paymentStatus = 'Pending';
  }
  
  next();
});

// Virtual for overdue status
BillSchema.virtual('isOverdue').get(function() {
  return this.paymentStatus !== 'Paid' && new Date() > this.dueDate;
});

export default mongoose.model<IBill>('Bill', BillSchema);
