import mongoose, { Schema, Document } from 'mongoose';

export interface IInventoryItem extends Document {
  itemId: string;
  name: string;
  description?: string;
  category: 'Medication' | 'Medical Equipment' | 'Surgical Instruments' | 'Consumables' | 'Laboratory Supplies';
  subcategory?: string;
  manufacturer?: string;
  supplier?: mongoose.Types.ObjectId;
  batchNumber?: string;
  expiryDate?: Date;
  unitOfMeasure: string;
  currentStock: number;
  minimumStock: number;
  maximumStock: number;
  reorderLevel: number;
  unitCost: number;
  sellingPrice?: number;
  location: {
    building?: string;
    floor?: string;
    room?: string;
    shelf?: string;
  };
  status: 'Active' | 'Inactive' | 'Expired' | 'Recalled';
  isControlledSubstance: boolean;
  requiresPrescription: boolean;
  storageConditions?: string;
  notes?: string;
  lastUpdatedBy: mongoose.Types.ObjectId;
}

const InventoryItemSchema: Schema = new Schema({
  itemId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'INV' + Date.now().toString().slice(-6);
    }
  },
  name: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  description: {
    type: String,
    trim: true
  },
  category: {
    type: String,
    required: true,
    enum: ['Medication', 'Medical Equipment', 'Surgical Instruments', 'Consumables', 'Laboratory Supplies'],
    index: true
  },
  subcategory: {
    type: String,
    trim: true
  },
  manufacturer: {
    type: String,
    trim: true
  },
  supplier: {
    type: Schema.Types.ObjectId,
    ref: 'Supplier'
  },
  batchNumber: {
    type: String,
    trim: true
  },
  expiryDate: {
    type: Date,
    index: true
  },
  unitOfMeasure: {
    type: String,
    required: true,
    trim: true
  },
  currentStock: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  minimumStock: {
    type: Number,
    required: true,
    min: 0,
    default: 10
  },
  maximumStock: {
    type: Number,
    required: true,
    min: 0
  },
  reorderLevel: {
    type: Number,
    required: true,
    min: 0
  },
  unitCost: {
    type: Number,
    required: true,
    min: 0
  },
  sellingPrice: {
    type: Number,
    min: 0
  },
  location: {
    building: String,
    floor: String,
    room: String,
    shelf: String
  },
  status: {
    type: String,
    enum: ['Active', 'Inactive', 'Expired', 'Recalled'],
    default: 'Active',
    index: true
  },
  isControlledSubstance: {
    type: Boolean,
    default: false
  },
  requiresPrescription: {
    type: Boolean,
    default: false
  },
  storageConditions: {
    type: String,
    trim: true
  },
  notes: {
    type: String,
    trim: true
  },
  lastUpdatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// Indexes
InventoryItemSchema.index({ itemId: 1 });
InventoryItemSchema.index({ name: 'text', description: 'text' });
InventoryItemSchema.index({ category: 1, subcategory: 1 });
InventoryItemSchema.index({ currentStock: 1 });
InventoryItemSchema.index({ expiryDate: 1 });

// Virtual for stock status
InventoryItemSchema.virtual('stockStatus').get(function() {
  if ((this.currentStock as any) <= 0) return 'Out of Stock';
  if ((this.currentStock as any) <= (this.reorderLevel as any)) return 'Low Stock';
  if ((this.currentStock as any) <= (this.minimumStock as any)) return 'Below Minimum';
  return 'In Stock';
});

// Virtual for days until expiry
InventoryItemSchema.virtual('daysUntilExpiry').get(function() {
  if (!this.expiryDate) return null;
  const today = new Date();
  const expiry = new Date(this.expiryDate);
  const diffTime = expiry.getTime() - today.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

export default mongoose.model<IInventoryItem>('InventoryItem', InventoryItemSchema);
