import mongoose, { Schema, Document } from 'mongoose';

export interface IMedicalRecord extends Document {
  recordId: string;
  patient: mongoose.Types.ObjectId;
  doctor: mongoose.Types.ObjectId;
  appointment?: mongoose.Types.ObjectId;
  visitDate: Date;
  chiefComplaint: string;
  historyOfPresentIllness: string;
  physicalExamination: {
    vitalSigns: {
      temperature: number;
      bloodPressure: {
        systolic: number;
        diastolic: number;
      };
      heartRate: number;
      respiratoryRate: number;
      oxygenSaturation: number;
      weight: number;
      height: number;
      bmi?: number;
    };
    generalAppearance: string;
    systemicExamination: string;
  };
  diagnosis: {
    primary: string;
    secondary?: string[];
    icdCodes?: string[];
  };
  treatment: {
    medications: Array<{
      name: string;
      dosage: string;
      frequency: string;
      duration: string;
      route: string;
      instructions?: string;
    }>;
    procedures?: Array<{
      name: string;
      date: Date;
      notes?: string;
    }>;
    recommendations: string[];
  };
  labResults?: Array<{
    testName: string;
    result: string;
    normalRange: string;
    date: Date;
    status: 'Normal' | 'Abnormal' | 'Critical';
  }>;
  imagingResults?: Array<{
    type: string;
    date: Date;
    findings: string;
    radiologist?: mongoose.Types.ObjectId;
  }>;
  followUp: {
    required: boolean;
    date?: Date;
    instructions?: string;
  };
  notes: string;
  status: 'Draft' | 'Completed' | 'Reviewed' | 'Archived';
  reviewedBy?: mongoose.Types.ObjectId;
  reviewedAt?: Date;
}

const MedicalRecordSchema: Schema = new Schema({
  recordId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'MR' + Date.now().toString().slice(-6);
    }
  },
  patient: {
    type: Schema.Types.ObjectId,
    ref: 'Patient',
    required: true
  },
  doctor: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  appointment: {
    type: Schema.Types.ObjectId,
    ref: 'Appointment'
  },
  visitDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  chiefComplaint: {
    type: String,
    required: true,
    trim: true
  },
  historyOfPresentIllness: {
    type: String,
    required: true,
    trim: true
  },
  physicalExamination: {
    vitalSigns: {
      temperature: { type: Number, min: 90, max: 110 },
      bloodPressure: {
        systolic: { type: Number, min: 70, max: 250 },
        diastolic: { type: Number, min: 40, max: 150 }
      },
      heartRate: { type: Number, min: 30, max: 200 },
      respiratoryRate: { type: Number, min: 8, max: 40 },
      oxygenSaturation: { type: Number, min: 70, max: 100 },
      weight: { type: Number, min: 0.5, max: 500 },
      height: { type: Number, min: 30, max: 250 },
      bmi: Number
    },
    generalAppearance: String,
    systemicExamination: String
  },
  diagnosis: {
    primary: { type: String, required: true },
    secondary: [String],
    icdCodes: [String]
  },
  treatment: {
    medications: [{
      name: { type: String, required: true },
      dosage: { type: String, required: true },
      frequency: { type: String, required: true },
      duration: { type: String, required: true },
      route: { type: String, required: true },
      instructions: String
    }],
    procedures: [{
      name: { type: String, required: true },
      date: { type: Date, required: true },
      notes: String
    }],
    recommendations: [String]
  },
  labResults: [{
    testName: { type: String, required: true },
    result: { type: String, required: true },
    normalRange: String,
    date: { type: Date, required: true },
    status: {
      type: String,
      enum: ['Normal', 'Abnormal', 'Critical'],
      required: true
    }
  }],
  imagingResults: [{
    type: { type: String, required: true },
    date: { type: Date, required: true },
    findings: { type: String, required: true },
    radiologist: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  followUp: {
    required: { type: Boolean, default: false },
    date: Date,
    instructions: String
  },
  notes: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    enum: ['Draft', 'Completed', 'Reviewed', 'Archived'],
    default: 'Draft'
  },
  reviewedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  reviewedAt: Date
}, {
  timestamps: true
});

// Calculate BMI before saving
MedicalRecordSchema.pre('save', function(next) {
  if (((this.physicalExamination as any)?.vitalSigns as any)?.weight) {
    const heightInMeters = ((this.physicalExamination as any).vitalSigns as any).height / 100;
    ((this.physicalExamination as any).vitalSigns as any).bmi = Number(
      ((this.physicalExamination as any).vitalSigns as any).weight / (heightInMeters * heightInMeters)
    );
  }
  next();
});

// Indexes
MedicalRecordSchema.index({ recordId: 1 });
MedicalRecordSchema.index({ patient: 1 });
MedicalRecordSchema.index({ doctor: 1 });
MedicalRecordSchema.index({ visitDate: 1 });
MedicalRecordSchema.index({ status: 1 });

export default mongoose.model<IMedicalRecord>('MedicalRecord', MedicalRecordSchema);
