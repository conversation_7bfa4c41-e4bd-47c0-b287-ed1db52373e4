import express from 'express';
import expressValidator from 'express-validator';
const body = (expressValidator as any).body;
const query = (expressValidator as any).query;
const param = (expressValidator as any).param;
import { getPatientVisits, getVisitStats, updateVisitStatus } from '../controllers/patientVisitController.js';
import { protect, checkPermission } from '../middleware/authMiddleware.js';

const router = express.Router();

// Validation rules
const createVisitValidation = [
  body('patient').isMongoId().withMessage('Valid patient ID is required'),
  body('doctor').isMongoId().withMessage('Valid doctor ID is required'),
  body('visitDate').isISO8601().withMessage('Valid visit date is required'),
  body('visitTime').notEmpty().withMessage('Visit time is required'),
  body('visitType').isIn(['Consultation', 'Follow-up', 'Emergency', 'Routine Check-up', 'Procedure', 'Vaccination', 'Screening'])
    .withMessage('Valid visit type is required'),
  body('department').notEmpty().withMessage('Department is required'),
  body('chiefComplaint').notEmpty().withMessage('Chief complaint is required'),
  body('historyOfPresentIllness').notEmpty().withMessage('History of present illness is required'),
  
  // Vital signs validation (optional)
  body('physicalExamination.vitalSigns.temperature').optional().isFloat({ min: 90, max: 110 })
    .withMessage('Temperature must be between 90-110'),
  body('physicalExamination.vitalSigns.bloodPressure.systolic').optional().isInt({ min: 60, max: 250 })
    .withMessage('Systolic BP must be between 60-250'),
  body('physicalExamination.vitalSigns.bloodPressure.diastolic').optional().isInt({ min: 40, max: 150 })
    .withMessage('Diastolic BP must be between 40-150'),
  body('physicalExamination.vitalSigns.heartRate').optional().isInt({ min: 30, max: 200 })
    .withMessage('Heart rate must be between 30-200'),
  body('physicalExamination.vitalSigns.respiratoryRate').optional().isInt({ min: 8, max: 40 })
    .withMessage('Respiratory rate must be between 8-40'),
  body('physicalExamination.vitalSigns.oxygenSaturation').optional().isInt({ min: 70, max: 100 })
    .withMessage('Oxygen saturation must be between 70-100'),
  body('physicalExamination.vitalSigns.weight').optional().isFloat({ min: 0, max: 1000 })
    .withMessage('Weight must be between 0-1000'),
  body('physicalExamination.vitalSigns.height').optional().isFloat({ min: 0, max: 300 })
    .withMessage('Height must be between 0-300'),
  
  // Assessment validation
  body('assessment.primaryDiagnosis.code').optional().notEmpty().withMessage('Primary diagnosis code is required if assessment provided'),
  body('assessment.primaryDiagnosis.description').optional().notEmpty().withMessage('Primary diagnosis description is required if assessment provided'),
  
  // Plan validation
  body('plan.medications').optional().isArray().withMessage('Medications must be an array'),
  body('plan.medications.*.name').optional().notEmpty().withMessage('Medication name is required'),
  body('plan.medications.*.dosage').optional().notEmpty().withMessage('Medication dosage is required'),
  body('plan.medications.*.frequency').optional().notEmpty().withMessage('Medication frequency is required'),
  body('plan.medications.*.duration').optional().notEmpty().withMessage('Medication duration is required'),
  body('plan.medications.*.instructions').optional().notEmpty().withMessage('Medication instructions are required'),
  
  body('visitNotes').notEmpty().withMessage('Visit notes are required'),
  body('visitDuration').optional().isInt({ min: 5, max: 480 }).withMessage('Visit duration must be between 5-480 minutes'),
  body('status').optional().isIn(['Scheduled', 'In Progress', 'Completed', 'Cancelled', 'No Show'])
    .withMessage('Invalid status')
];

const updateVisitValidation = [
  body('patient').optional().isMongoId().withMessage('Valid patient ID is required'),
  body('doctor').optional().isMongoId().withMessage('Valid doctor ID is required'),
  body('visitDate').optional().isISO8601().withMessage('Valid visit date is required'),
  body('visitTime').optional().notEmpty().withMessage('Visit time is required'),
  body('visitType').optional().isIn(['Consultation', 'Follow-up', 'Emergency', 'Routine Check-up', 'Procedure', 'Vaccination', 'Screening'])
    .withMessage('Valid visit type is required'),
  body('department').optional().notEmpty().withMessage('Department is required'),
  body('chiefComplaint').optional().notEmpty().withMessage('Chief complaint is required'),
  body('historyOfPresentIllness').optional().notEmpty().withMessage('History of present illness is required'),
  
  // Vital signs validation (optional)
  body('physicalExamination.vitalSigns.temperature').optional().isFloat({ min: 90, max: 110 })
    .withMessage('Temperature must be between 90-110'),
  body('physicalExamination.vitalSigns.bloodPressure.systolic').optional().isInt({ min: 60, max: 250 })
    .withMessage('Systolic BP must be between 60-250'),
  body('physicalExamination.vitalSigns.bloodPressure.diastolic').optional().isInt({ min: 40, max: 150 })
    .withMessage('Diastolic BP must be between 40-150'),
  body('physicalExamination.vitalSigns.heartRate').optional().isInt({ min: 30, max: 200 })
    .withMessage('Heart rate must be between 30-200'),
  body('physicalExamination.vitalSigns.respiratoryRate').optional().isInt({ min: 8, max: 40 })
    .withMessage('Respiratory rate must be between 8-40'),
  body('physicalExamination.vitalSigns.oxygenSaturation').optional().isInt({ min: 70, max: 100 })
    .withMessage('Oxygen saturation must be between 70-100'),
  body('physicalExamination.vitalSigns.weight').optional().isFloat({ min: 0, max: 1000 })
    .withMessage('Weight must be between 0-1000'),
  body('physicalExamination.vitalSigns.height').optional().isFloat({ min: 0, max: 300 })
    .withMessage('Height must be between 0-300'),
  
  body('visitNotes').optional().notEmpty().withMessage('Visit notes cannot be empty'),
  body('visitDuration').optional().isInt({ min: 5, max: 480 }).withMessage('Visit duration must be between 5-480 minutes'),
  body('status').optional().isIn(['Scheduled', 'In Progress', 'Completed', 'Cancelled', 'No Show'])
    .withMessage('Invalid status')
];

const queryValidation = [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('patient').optional().isMongoId().withMessage('Valid patient ID is required'),
  query('doctor').optional().isMongoId().withMessage('Valid doctor ID is required'),
  query('department').optional().notEmpty().withMessage('Department cannot be empty'),
  query('visitType').optional().isIn(['Consultation', 'Follow-up', 'Emergency', 'Routine Check-up', 'Procedure', 'Vaccination', 'Screening'])
    .withMessage('Invalid visit type'),
  query('status').optional().isIn(['Scheduled', 'In Progress', 'Completed', 'Cancelled', 'No Show'])
    .withMessage('Invalid status'),
  query('startDate').optional().isISO8601().withMessage('Valid start date is required'),
  query('endDate').optional().isISO8601().withMessage('Valid end date is required'),
  query('search').optional().isLength({ min: 2 }).withMessage('Search query must be at least 2 characters')
];

const paramValidation = [
  param('id').isMongoId().withMessage('Valid visit ID is required')
];

const patientParamValidation = [
  param('patientId').isMongoId().withMessage('Valid patient ID is required')
];

const statusUpdateValidation = [
  body('status').isIn(['Scheduled', 'In Progress', 'Completed', 'Cancelled', 'No Show'])
    .withMessage('Valid status is required')
];

// Apply authentication to all routes
router.use(protect);

// Routes
router.route('/')
  .get(checkPermission('patients', 'view'), queryValidation, getPatientVisits)
  .post(checkPermission('patients', 'create'), createVisitValidation, createPatientVisit);

// Stats route
router.get('/stats', checkPermission('patients', 'view'), getVisitStats);

// Patient visit history
router.get('/patient/:patientId/history', 
  checkPermission('patients', 'view'), 
  patientParamValidation, 
  queryValidation, 
  getPatientVisitHistory
);

// Individual visit routes
router.route('/:id')
  .get(checkPermission('patients', 'view'), paramValidation, getPatientVisit)
  .put(checkPermission('patients', 'edit'), paramValidation, updateVisitValidation, updatePatientVisit)
  .delete(checkPermission('patients', 'delete'), paramValidation, deletePatientVisit);

// Status update route
router.patch('/:id/status', 
  checkPermission('patients', 'edit'), 
  paramValidation, 
  statusUpdateValidation, 
  updateVisitStatus
);

export default router;
