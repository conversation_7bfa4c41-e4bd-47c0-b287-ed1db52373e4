import express from 'express';
import expressValidator from 'express-validator';
const body = (expressValidator as any).body;
const query = (expressValidator as any).query;
import {
  getPatients,
  getPatient,
  createPatient,
  updatePatient,
  deletePatient,
  searchPatients,
  getPatientMedicalHistory,
  getPatientAppointments,
  admitPatient,
  dischargePatient
} from '../controllers/patientController.js';
import { protect, checkPermission } from '../middleware/authMiddleware.js';

const router = express.Router();

// Validation rules
const createPatientValidation = [
  body('firstName').notEmpty().trim().withMessage('First name is required'),
  body('lastName').notEmpty().trim().withMessage('Last name is required'),
  body('dateOfBirth').isISO8601().withMessage('Valid date of birth is required'),
  body('gender').isIn(['Male', 'Female', 'Other']).withMessage('Valid gender is required'),
  body('phone').notEmpty().trim().withMessage('Phone number is required'),
  body('address.street').notEmpty().trim().withMessage('Street address is required'),
  body('address.city').notEmpty().trim().withMessage('City is required'),
  body('address.state').notEmpty().trim().withMessage('State is required'),
  body('address.zipCode').notEmpty().trim().withMessage('Zip code is required'),
  body('emergencyContact.name').notEmpty().trim().withMessage('Emergency contact name is required'),
  body('emergencyContact.relationship').notEmpty().trim().withMessage('Emergency contact relationship is required'),
  body('emergencyContact.phone').notEmpty().trim().withMessage('Emergency contact phone is required')
];

const updatePatientValidation = [
  body('firstName').optional().notEmpty().trim().withMessage('First name cannot be empty'),
  body('lastName').optional().notEmpty().trim().withMessage('Last name cannot be empty'),
  body('dateOfBirth').optional().isISO8601().withMessage('Valid date of birth is required'),
  body('gender').optional().isIn(['Male', 'Female', 'Other']).withMessage('Valid gender is required'),
  body('phone').optional().notEmpty().trim().withMessage('Phone number cannot be empty'),
  body('email').optional().isEmail().normalizeEmail().withMessage('Valid email is required')
];

const searchValidation = [
  query('q').optional().isLength({ min: 2 }).withMessage('Search query must be at least 2 characters'),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
];

// Apply authentication to all routes
router.use(protect);

// Routes
router.route('/')
  .get(checkPermission('patients', 'view'), searchValidation, getPatients)
  .post(checkPermission('patients', 'create'), createPatientValidation, createPatient);

router.get('/search', checkPermission('patients', 'view'), searchValidation, searchPatients);

router.route('/:id')
  .get(checkPermission('patients', 'view'), getPatient)
  .put(checkPermission('patients', 'edit'), updatePatientValidation, updatePatient)
  .delete(checkPermission('patients', 'delete'), deletePatient);

router.get('/:id/medical-history', checkPermission('patients', 'view'), getPatientMedicalHistory);
router.get('/:id/appointments', checkPermission('patients', 'view'), getPatientAppointments);

router.post('/:id/admit', checkPermission('patients', 'edit'), admitPatient);
router.post('/:id/discharge', checkPermission('patients', 'edit'), dischargePatient);

export default router;
