import express from 'express';
import expressValidator from 'express-validator';
const body = (expressValidator as any).body;
import {
  getAppointments,
  getAppointment,
  createAppointment,
  updateAppointment,
  deleteAppointment,
  getDoctorSchedule,
  getAvailableSlots
} from '../controllers/appointmentController.js';
import { protect, checkPermission } from '../middleware/authMiddleware.js';

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Routes
router.route('/')
  .get(checkPermission('appointments', 'view'), getAppointments)
  .post(checkPermission('appointments', 'create'), createAppointment);

router.get('/doctor/:doctorId/schedule', checkPermission('appointments', 'view'), getDoctorSchedule);
router.get('/available-slots', checkPermission('appointments', 'view'), getAvailableSlots);

router.route('/:id')
  .get(checkPermission('appointments', 'view'), getAppointment)
  .put(checkPermission('appointments', 'edit'), updateAppointment)
  .delete(checkPermission('appointments', 'delete'), deleteAppointment);

export default router;
