#!/bin/bash

# Hospital Management System - Render Deployment Script
echo "🏥 Hospital Management System - Render Deployment"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if git is initialized
if [ ! -d ".git" ]; then
    print_error "Git repository not initialized. Please run:"
    echo "git init"
    echo "git add ."
    echo "git commit -m 'Initial commit'"
    exit 1
fi

# Check if remote is set
if ! git remote get-url origin &> /dev/null; then
    print_warning "No remote repository set. Please add your GitHub repository:"
    echo "git remote add origin https://github.com/yourusername/your-repo.git"
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_warning "No .env file found. Creating from template..."
    if [ -f "env.example" ]; then
        cp env.example .env
        print_info "Please edit .env file with your configuration"
    else
        print_error "env.example file not found"
        exit 1
    fi
fi

# Build the application
print_info "Building the application..."
npm run build
if [ $? -eq 0 ]; then
    print_status "Application built successfully"
else
    print_error "Failed to build application"
    exit 1
fi

# Commit and push changes
print_info "Committing and pushing changes..."
git add .
git commit -m "Prepare for Render deployment"
git push origin main

if [ $? -eq 0 ]; then
    print_status "Changes pushed to GitHub successfully"
else
    print_error "Failed to push changes to GitHub"
    exit 1
fi

print_status "Deployment preparation completed!"
echo ""
print_info "📋 Next Steps for Render Deployment:"
echo ""
echo "1. Go to https://render.com"
echo "2. Sign up/Login with your GitHub account"
echo "3. Click 'New +' → 'Web Service'"
echo "4. Connect your GitHub repository"
echo "5. Configure the backend service:"
echo "   - Name: hms-backend"
echo "   - Environment: Node"
echo "   - Build Command: npm install && npm run build:server"
echo "   - Start Command: npm run server:only"
echo ""
echo "6. Set Environment Variables:"
echo "   - NODE_ENV=production"
echo "   - MONGODB_URI=your_mongodb_atlas_connection_string"
echo "   - JWT_SECRET=your_secure_jwt_secret_key"
echo "   - CLIENT_URL=https://hms-frontend.onrender.com"
echo "   - PORT=3002"
echo ""
echo "7. Deploy the backend service"
echo ""
echo "8. Create frontend service:"
echo "   - Click 'New +' → 'Static Site'"
echo "   - Name: hms-frontend"
echo "   - Build Command: npm install && npm run build:client"
echo "   - Publish Directory: dist"
echo "   - Environment Variable: VITE_REACT_APP_API_URL=https://hms-backend.onrender.com/api"
echo ""
print_info "🔗 After deployment, your URLs will be:"
echo "- Backend: https://hms-backend.onrender.com"
echo "- Frontend: https://hms-frontend.onrender.com"
echo ""
print_info "🔐 Default Login:"
echo "- Admin: <EMAIL> / admin123"
echo ""
print_status "Ready for Render deployment! 🚀" 