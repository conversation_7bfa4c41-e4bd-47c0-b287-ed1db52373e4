# Hospital Management System - Deployment Guide

## 🚀 Deployment Options

This guide covers deployment to **Render** (Recommended) and **Vercel** as alternatives.

## 📋 Prerequisites

1. **MongoDB Atlas Database** (Already configured)
2. **GitHub Repository** (Code should be pushed to GitHub)
3. **Environment Variables** (Configured in deployment platform)

## 🎯 Option 1: Render (Recommended)

### Why Render?
- ✅ Free tier for both frontend and backend
- ✅ Easy environment variable management
- ✅ Automatic deployments from GitHub
- ✅ Better for full-stack applications
- ✅ No cold start issues

### Step-by-Step Deployment

#### 1. Prepare Your Repository

```bash
# Ensure all files are committed
git add .
git commit -m "Prepare for deployment"
git push origin main
```

#### 2. Create Render Account
1. Go to [render.com](https://render.com)
2. Sign up with your GitHub account
3. Connect your GitHub repository

#### 3. Deploy Backend Service

1. **Create New Web Service**
   - Click "New +" → "Web Service"
   - Connect your GitHub repository
   - Name: `hms-backend`
   - Environment: `Node`
   - Region: Choose closest to your users
   - Branch: `main`

2. **Configure Build Settings**
   ```
   Build Command: npm install && npm run build:server
   Start Command: npm run server:only
   ```

3. **Set Environment Variables**
   ```
   NODE_ENV=production
   MONGODB_URI=your_mongodb_atlas_connection_string
   JWT_SECRET=your_secure_jwt_secret_key
   CLIENT_URL=https://hms-frontend.onrender.com
   PORT=3002
   ```

4. **Deploy**
   - Click "Create Web Service"
   - Wait for build to complete
   - Note the URL (e.g., `https://hms-backend.onrender.com`)

#### 4. Deploy Frontend Service

1. **Create New Static Site**
   - Click "New +" → "Static Site"
   - Connect your GitHub repository
   - Name: `hms-frontend`
   - Branch: `main`

2. **Configure Build Settings**
   ```
   Build Command: npm install && npm run build:client
   Publish Directory: dist
   ```

3. **Set Environment Variables**
   ```
   VITE_REACT_APP_API_URL=https://hms-backend.onrender.com/api
   ```

4. **Deploy**
   - Click "Create Static Site"
   - Wait for build to complete
   - Your frontend will be available at the provided URL

#### 5. Update Backend CORS

After frontend deployment, update the backend's `CLIENT_URL` environment variable to match your frontend URL.

#### 6. Test Deployment

1. **Health Check**: Visit `https://hms-backend.onrender.com/health`
2. **API Test**: Visit `https://hms-backend.onrender.com/api/test`
3. **Frontend**: Visit your frontend URL

## 🎯 Option 2: Vercel

### Why Vercel?
- ✅ Excellent for React applications
- ✅ Fast deployments
- ✅ Good free tier
- ⚠️ Limited for backend (serverless functions)

### Step-by-Step Deployment

#### 1. Install Vercel CLI

```bash
npm install -g vercel
```

#### 2. Configure Vercel

```bash
# Login to Vercel
vercel login

# Initialize project
vercel
```

#### 3. Set Environment Variables

```bash
vercel env add MONGODB_URI
vercel env add JWT_SECRET
vercel env add NODE_ENV production
```

#### 4. Deploy

```bash
vercel --prod
```

## 🔧 Environment Variables Setup

### Required Variables

```bash
# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/hospital_management

# JWT Security
JWT_SECRET=your_very_secure_secret_key_here
JWT_EXPIRE=24h

# Server Configuration
NODE_ENV=production
PORT=3002

# Client URL (Update after deployment)
CLIENT_URL=https://your-frontend-url.com
```

### Optional Variables

```bash
# Email (if using email features)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# File Upload
MAX_FILE_SIZE=********
UPLOAD_PATH=./uploads
```

## 🗄️ Database Setup

### 1. MongoDB Atlas Configuration

1. **Create Database**
   ```javascript
   // Connect to your MongoDB Atlas cluster
   // Database name: hospital_management
   ```

2. **Seed Initial Data**
   ```bash
   # After deployment, run seeding
   npm run seed:admin
   ```

### 2. Default Admin Account

After seeding, you can login with:
- **Email**: <EMAIL>
- **Password**: admin123

## 🔍 Post-Deployment Checklist

### ✅ Backend Verification

1. **Health Check**
   ```bash
   curl https://your-backend-url.com/health
   ```

2. **API Test**
   ```bash
   curl https://your-backend-url.com/api/test
   ```

3. **Database Connection**
   - Check server logs for MongoDB connection success

### ✅ Frontend Verification

1. **Load Application**
   - Visit your frontend URL
   - Check for any console errors

2. **API Integration**
   - Test login functionality
   - Verify API calls are working

3. **Environment Variables**
   - Ensure `VITE_REACT_APP_API_URL` is correct

### ✅ Security Verification

1. **HTTPS**
   - Ensure all URLs use HTTPS
   - Check SSL certificates

2. **CORS**
   - Verify CORS is properly configured
   - Test cross-origin requests

3. **Environment Variables**
   - Ensure sensitive data is not exposed
   - Verify JWT_SECRET is set

## 🚨 Troubleshooting

### Common Issues

#### 1. Build Failures
```bash
# Check build logs
# Ensure all dependencies are in package.json
npm install
npm run build
```

#### 2. Database Connection Issues
```bash
# Verify MongoDB URI
# Check network access
# Ensure database exists
```

#### 3. CORS Errors
```javascript
// Update CORS configuration in server/index.js
app.use(cors({
  origin: process.env.CLIENT_URL,
  credentials: true
}));
```

#### 4. Environment Variables
```bash
# Verify all required variables are set
# Check variable names match code
```

### Performance Optimization

#### 1. Enable Compression
```javascript
// Already configured in server/index.js
app.use(compression());
```

#### 2. Rate Limiting
```javascript
// Already configured
const limiter = rateLimit({
  windowMs: 1 * 60 * 1000,
  max: 1000
});
```

#### 3. Security Headers
```javascript
// Already configured with helmet
app.use(helmet());
```

## 📊 Monitoring

### 1. Health Checks
- Monitor `/health` endpoint
- Set up uptime monitoring

### 2. Error Tracking
- Check deployment platform logs
- Monitor application errors

### 3. Performance
- Monitor response times
- Check memory usage

## 🔄 Continuous Deployment

### Automatic Deployments
- Connect GitHub repository
- Enable automatic deployments on push
- Set up branch protection rules

### Manual Deployments
```bash
# Render: Automatic from GitHub
# Vercel: vercel --prod
```

## 📞 Support

### Render Support
- [Render Documentation](https://render.com/docs)
- [Render Community](https://community.render.com)

### Vercel Support
- [Vercel Documentation](https://vercel.com/docs)
- [Vercel Community](https://github.com/vercel/vercel/discussions)

---

## 🎉 Success!

Your Hospital Management System is now deployed and ready to use!

**Access URLs:**
- Frontend: `https://your-frontend-url.com`
- Backend API: `https://your-backend-url.com/api`
- Health Check: `https://your-backend-url.com/health`

**Default Login:**
- Admin: <EMAIL> / admin123
- Doctor: <EMAIL> / doctor123 