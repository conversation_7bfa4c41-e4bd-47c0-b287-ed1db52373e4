services:
  # Backend API Service
  - type: web
    name: hms-backend
    env: node
    plan: free
    buildCommand: npm install && npm run build:server
    startCommand: npm run server:only
    envVars:
      - key: NODE_ENV
        value: production
      - key: MONGODB_URI
        sync: false
      - key: JWT_SECRET
        generateValue: true
      - key: CLIENT_URL
        value: https://hms-frontend.onrender.com
      - key: PORT
        value: 3002

  # Frontend Service
  - type: web
    name: hms-frontend
    env: static
    plan: free
    buildCommand: npm install && npm run build:client
    staticPublishPath: ./dist
    envVars:
      - key: VITE_REACT_APP_API_URL
        value: https://hms-backend.onrender.com/api 