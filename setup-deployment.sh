#!/bin/bash

# Hospital Management System - Deployment Setup
echo "🏥 Hospital Management System - Deployment Setup"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18 or higher."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18 or higher is required. Current version: $(node -v)"
    exit 1
fi

print_status "Node.js version check passed: $(node -v)"

# Install dependencies
print_info "Installing dependencies..."
npm install
if [ $? -eq 0 ]; then
    print_status "Dependencies installed successfully"
else
    print_error "Failed to install dependencies"
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    print_info "Creating .env file from template..."
    if [ -f "env.example" ]; then
        cp env.example .env
        print_status ".env file created"
        print_warning "Please edit .env file with your MongoDB Atlas connection string and other configuration"
    else
        print_error "env.example file not found"
        exit 1
    fi
else
    print_status ".env file already exists"
fi

# Test build
print_info "Testing build process..."
npm run build
if [ $? -eq 0 ]; then
    print_status "Build test successful"
else
    print_error "Build test failed"
    exit 1
fi

# Check if git is initialized
if [ ! -d ".git" ]; then
    print_warning "Git repository not initialized. Initializing..."
    git init
    git add .
    git commit -m "Initial commit"
    print_status "Git repository initialized"
else
    print_status "Git repository already exists"
fi

print_status "Setup completed successfully!"
echo ""
print_info "📋 Next Steps:"
echo "1. Edit .env file with your MongoDB Atlas connection string"
echo "2. Push your code to GitHub:"
echo "   git remote add origin https://github.com/yourusername/your-repo.git"
echo "   git push -u origin main"
echo "3. Run ./deploy-render.sh to prepare for Render deployment"
echo ""
print_info "🔧 Required Environment Variables:"
echo "- MONGODB_URI: Your MongoDB Atlas connection string"
echo "- JWT_SECRET: A secure random string for JWT tokens"
echo "- CLIENT_URL: Your frontend URL (set after deployment)"
echo ""
print_status "Ready for deployment! 🚀" 